<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.CreditCardReminderNotifierMapper">


    <select id="selectNotifierByCreditCardIds"
        resultType="com.get.insurancecenter.entity.CreditCardReminderNotifier"> SELECT n.*,
        r.fk_credit_card_id as creditCardId FROM m_credit_card_reminder_notifier n JOIN
        m_credit_card_reminder r ON n.fk_credit_card_reminder_id = r.id where (r.is_quota_remind = 1
        OR r.is_failed_remind = 1 OR r.is_repayment_remind = 1 OR r.is_transaction_remind = 1) and
        r.fk_credit_card_id in <foreach item="item" index="index" collection="creditCardIds"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>