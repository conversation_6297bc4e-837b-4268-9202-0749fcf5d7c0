package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.officecenter.service.IAiService;
import com.get.officecenter.vo.LeaveSummaryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "Ai管理")
@RestController
@RequestMapping("office/ai")
public class AiController {
    @Resource
    private IAiService aiService;

    // 查询自己的请假信息
    @ApiOperation(value = "查询自己的请假信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/Ai管理/查询自己的请假信息")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getMyLeaveApplicationForms")
    public ResponseBo<LeaveSummaryVo> getMyLeaveApplicationForms(@RequestParam String startTime,
                                                                @RequestParam String endTime,
                                                                @RequestHeader("at") String token) {
        return new ResponseBo<>(aiService.getMyLeaveApplicationForms(startTime, endTime));
    }

    // 查询需要审批的请假信息
    @ApiOperation(value = "查询需要审批的请假信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/Ai管理/查询需要审批的请假信息")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getApprovalLeaveApplicationForms")
    public ResponseBo<LeaveSummaryVo> getApprovalLeaveApplicationForms(@RequestParam String startTime,
                                                                       @RequestParam String endTime,
                                                                       @RequestHeader("at") String token) {
        return new ResponseBo<>(aiService.getApprovalLeaveApplicationForms(startTime, endTime));
    }

    //查询所有的请假信息
    @ApiOperation(value = "查询所有的请假信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/Ai管理/查询所有的请假信息")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getAllLeaveApplicationForms")
    public ResponseBo<LeaveSummaryVo> getAllLeaveApplicationForms(@RequestParam String startTime,
                                                                  @RequestParam String endTime,
                                                                  @RequestHeader("at") String token) {
        return new ResponseBo<>(aiService.getAllLeaveApplicationForms(startTime, endTime));
    }

}
