package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("m_credit_card_reminder")
public class CreditCardReminder extends BaseEntity {

    @ApiModelProperty(value = "信用卡Id")
    private Long fkCreditCardId;

    @ApiModelProperty(value = "交易提醒，是否开启：0否/1是")
    private Integer isTransactionRemind;

    @ApiModelProperty(value = "交易提醒方式，多选，逗号相隔：1,2。1邮件/2短信")
    private String transactionRemindType;

    @ApiModelProperty(value = "额度提醒，是否开启：0否/1是")
    private Integer isQuotaRemind;

    @ApiModelProperty(value = "额度提醒方式，多选，逗号相隔：1,2。1邮件/2短信")
    private String quotaRemindType;

    @ApiModelProperty(value = "额度提醒金额（小于等于进行提醒）")
    private BigDecimal quotaLimit;

    @ApiModelProperty(value = "额度提醒天数（0提醒1次，1为每天提醒，默认9点提醒）")
    private Integer quotaRemindDays;

    @ApiModelProperty(value = "支付失败提醒，是否开启：0否/1是")
    private Integer isFailedRemind;

    @ApiModelProperty(value = "支付失败提醒方式，多选，逗号相隔：1,2。1邮件/2短信")
    private String failedRemindType;

    @ApiModelProperty(value = "出账还款提醒，是否开启：0否/1是")
    private Integer isRepaymentRemind;

    @ApiModelProperty(value = "出账还款提醒方式，多选，逗号相隔：1,2。1邮件/2短信")
    private String repaymentRemindType;

    @ApiModelProperty(value = "提醒通知人信息")
    @TableField(exist = false)
    private List<CreditCardReminderNotifier> creditCardReminderNotifierList;
}