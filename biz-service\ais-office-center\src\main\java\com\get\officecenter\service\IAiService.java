package com.get.officecenter.service;

import com.get.officecenter.dto.query.AiLeaveQueryParam;
import com.get.officecenter.vo.LeaveSummaryVo;

public interface IAiService {

    /**
     * 获取个人请假列表
     * @param aiLeaveQueryParam
     * @return
     */
    LeaveSummaryVo getMyLeaveApplicationForms(AiLeaveQueryParam aiLeaveQueryParam);

    /**
     * 获取需要审批请假列表
     * @param aiLeaveQueryParam
     * @return
     */
    LeaveSummaryVo getApprovalLeaveApplicationForms(AiLeaveQueryParam aiLeaveQueryParam);

    /**
     * 获取所有请假记录
     * @param aiLeaveQueryParam
     * @return
     */
    LeaveSummaryVo getAllLeaveApplicationForms(AiLeaveQueryParam aiLeaveQueryParam);


}
