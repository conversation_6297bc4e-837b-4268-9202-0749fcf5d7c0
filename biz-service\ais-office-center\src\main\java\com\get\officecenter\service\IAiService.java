package com.get.officecenter.service;

import com.get.officecenter.vo.LeaveSummaryVo;

public interface IAiService {

    /**
     * 获取个人请假列表
     * @param startTime
     * @param endTime
     * @return
     */
    LeaveSummaryVo getMyLeaveApplicationForms(String startTime, String endTime);

    /**
     * 获取需要审批请假列表
     * @param startTime
     * @param endTime
     * @return
     */
    LeaveSummaryVo getApprovalLeaveApplicationForms(String startTime, String endTime);

    /**
     * 获取所有请假记录
     * @param startTime
     * @param endTime
     * @return
     */
    LeaveSummaryVo getAllLeaveApplicationForms(String startTime, String endTime);


}
