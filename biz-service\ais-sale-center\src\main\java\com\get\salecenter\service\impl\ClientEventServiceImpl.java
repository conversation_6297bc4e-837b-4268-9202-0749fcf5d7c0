package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.dto.RemindTaskUpdateDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.ClientEventMapper;
import com.get.salecenter.dao.sale.StudentEventTypeMapper;
import com.get.salecenter.dao.sale.StudentMapper;
import com.get.salecenter.dto.ClientEventAddDto;
import com.get.salecenter.dto.ClientEventDto;
import com.get.salecenter.dto.ClientEventRemindDto;
import com.get.salecenter.entity.ClientEvent;
import com.get.salecenter.service.IClientEventService;
import com.get.salecenter.service.IClientService;
import com.get.salecenter.service.IStudentEventTypeService;
import com.get.salecenter.vo.ClientEventVo;
import com.get.salecenter.vo.ClientVo;
import com.get.salecenter.vo.StudentEventTypeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * author:Neil
 * Time: 14:11
 * Date: 2022/8/17
 * Description:
 */
@Service
@Slf4j
public class ClientEventServiceImpl implements IClientEventService {


    @Resource
    private ClientEventMapper clientEventMapper;

    @Resource
    private IStudentEventTypeService typeService;

    @Resource
    private UtilService utilService;

    @Resource
    private StudentEventTypeMapper studentEventTypeMapper;

    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private IClientService clientService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;


    @Override
    public List<ClientEventVo> getClientEvents(ClientEventDto clientEventDto, SearchBean<ClientEventDto> page) {
        LambdaQueryWrapper<ClientEvent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(clientEventDto)) {
            if (GeneralTool.isNotEmpty(clientEventDto.getFkClientId())) {
                lambdaQueryWrapper.eq(ClientEvent::getFkClientId, clientEventDto.getFkClientId());
            }
            if (GeneralTool.isNotEmpty(clientEventDto.getFkStudentEventTypeId())) {
                lambdaQueryWrapper.eq(ClientEvent::getFkStudentEventTypeId, clientEventDto.getFkStudentEventTypeId());
            }
            if (GeneralTool.isNotEmpty(clientEventDto.getKeyWord())) {
                lambdaQueryWrapper.like(ClientEvent::getDescription, clientEventDto.getKeyWord());
            }
        }
        IPage<ClientEvent> iPage = clientEventMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())),
                lambdaQueryWrapper);
        List<ClientEvent> clientEvents = iPage.getRecords();
        page.setAll((int) iPage.getTotal());

        List<ClientEventVo> collect =
                clientEvents.stream().map(clientEvent -> BeanCopyUtils.objClone(clientEvent, ClientEventVo::new)).collect(Collectors.toList());


        //设置类型名称
        if (GeneralTool.isNotEmpty(collect)) {
            if (GeneralTool.isNotEmpty(collect)) {
                for (ClientEventVo clientEventVo : collect) {
                    if (GeneralTool.isNotEmpty(clientEventVo.getFkStudentEventTypeId())) {
                        StudentEventTypeVo studentEventType = typeService.findStudentEventTypeById(clientEventVo.getFkStudentEventTypeId());
                        if (GeneralTool.isNotEmpty(studentEventType)) {
                            clientEventVo.setFkStudentEventTypeName(studentEventType.getTypeName());
                        }
                    }
                }
            }
        }
        return collect;
    }

    @Override
    public Long addClientEvent(ClientEventAddDto clientEventVo) {
        if (GeneralTool.isEmpty(clientEventVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ClientEvent clientEvent = BeanCopyUtils.objClone(clientEventVo, ClientEvent::new);
        utilService.setCreateInfo(clientEvent);
        clientEventMapper.insert(clientEvent);
        return clientEvent.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //查询客户事件
        ClientEvent clientEvent = clientEventMapper.selectById(id);
        try {
            if(GeneralTool.isNotEmpty(clientEvent)&&GeneralTool.isNotEmpty(clientEvent.getFollowUpTime())){
                List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
                RemindTaskDto remindTaskDto = new RemindTaskDto();
                remindTaskDto.setFkTableId(clientEvent.getId());
                remindTaskDto.setFkTableName(TableEnum.SALE_CLIENT.key);
                remindTaskDto.setFkRemindEventTypeKey(EmailTemplateEnum.FOLLOW_UP_APPOINTMENT.getEmailTemplateKey());
                remindTaskDtos.add(remindTaskDto);
                reminderCenterClient.deleteEmailQueueByTableId(remindTaskDtos);
            }
        }catch (Exception e){
           log.error("删除提醒任务失败",e);
        }

        clientEventMapper.deleteById(id);
    }

    @Override
    public void addRemindTaskEven(ClientEventRemindDto clientEventRemindDto) {
        if (GeneralTool.isEmpty(clientEventRemindDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
//        ClientEvent clientEvent1 = clientEventMapper.selectOne(Wrappers.<ClientEvent>lambdaQuery().eq(ClientEvent::getFkClientId,clientEventRemindDto.getFkClientId()));
        ClientVo clientVo = clientService.findClientById(clientEventRemindDto.getFkClientId());
        if (GeneralTool.isEmpty(clientVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_empty"));
        }
        //判断时间是否小于当前时间
//        if (clientEventRemindDto.getFollowUpTime().before(new Date()))
//            throw new GetServiceException(LocaleMessageUtils.getMessage("task_start_time_need_greater_now"));
        //获取key为FILE_SRC_PREFIX的配置信息
        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
        String link = configVo.getValue1();
//        String link="bms.geteducation.net";
//        if (interfaceConfiguration.equals("HTI")){
//            link="bms.ht-international.net";
//        }
        //RemindTaskUpdateDto remindTaskUpdateVo = new RemindTaskUpdateDto();
        RemindTaskDto remindTaskUpdateVo = new RemindTaskDto();
        String[] remindMethod = {"1", "3"};
//        String taskLink = "https://"+link+"/sales-center/repository-management/repository-detail/"
//                + clientEventRemindDto.getFkClientId()
//                + "?tabtype=event&clientId="
//                + clientEventRemindDto.getFkClientId();
//        remindTaskUpdateVo.setTaskLink(taskLink);

//        remindTaskUpdateVo.setTaskRemark(
//                "<div class=\"desc\">" +
//                        "<div>"+ clientEventRemindDto.getDescription()+"</div>"
//                        + "<br>"
//                        + "<br>"
//                        + "<div>【"
//                        + clientVo.getName()
//                        +"】【"
//                        + clientVo.getNum()
//                        +"】</div>"
//                        + "<div> <a href=\""+taskLink+"\">【邮件可点击查看学生详情】</a></div>" +
//                        "</div>"
//        );


//        String taskLink = link + "/sales-center/repository-management/repository-detail/"
//                + clientEventRemindDto.getFkClientId()
//                + "?tabtype=event&clientId="
//                + clientEventRemindDto.getFkClientId();

        String taskLink = link + "/sales-center_repository-management_repository-detail/"
                + clientEventRemindDto.getFkClientId()
                + "?tabtype=event&clientId="
                + clientEventRemindDto.getFkClientId();

//        RemindTaskUpdateDto remindTaskUpdateVo = new RemindTaskUpdateDto();
        remindTaskUpdateVo.setTaskLink(taskLink);
        //TODO 邮件可点击查看学生详情
        //        转base64
        String taskRemark = Base64.getEncoder().encodeToString((
                "<div class=\"desc\">" +
                        "<div>" + clientEventRemindDto.getDescription() + "</div>" +
                        "<br>" +
                        "<br>" +
                        "<div>【" + clientVo.getName() + "】【" + clientVo.getNum() + "】</div>" +
                        "<div> <a href= " + taskLink + ">【邮件可点击查看学生详情】</a></div>" +
                        "</div>").getBytes(StandardCharsets.UTF_8));

        ClientEvent clientEvent = BeanCopyUtils.objClone(clientEventRemindDto, ClientEvent::new);
        clientEvent.setFkStudentEventTypeId(5L);
        utilService.setCreateInfo(clientEvent);
        clientEventMapper.insert(clientEvent);

        Long fkStaffId = SecureUtil.getStaffId();
        remindTaskUpdateVo.setTaskRemark(taskRemark);
        remindTaskUpdateVo.setStartTime(clientEventRemindDto.getFollowUpTime());
        //TODO FOLLOW_UP_APPOINTMENT
        remindTaskUpdateVo.setTaskTitle("【" + clientVo.getName() + "】预约回访");
        remindTaskUpdateVo.setFkRemindEventTypeKey("FOLLOW_UP_APPOINTMENT");
        remindTaskUpdateVo.setRemindMethod("1");
        remindTaskUpdateVo.setFkStaffId(fkStaffId);
        remindTaskUpdateVo.setStatus(1);
        List<RemindTaskDto> remindTaskDtos =  new ArrayList<>();
        remindTaskDtos.add(remindTaskUpdateVo);
        //remindTaskUpdateVo.setAdvanceHours("0");
        //Result result = reminderCenterClient.add(remindTaskUpdateVo);
        Result result = reminderCenterClient.batchAddTask(remindTaskDtos);

        List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
        emailSenderQueue.setFkTableId(clientVo.getId());
        emailSenderQueue.setFkTableName(TableEnum.SALE_CLIENT.key);
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.FOLLOW_UP_APPOINTMENT.getEmailTemplateKey());
        emailSenderQueue.setOperationTime(clientEventRemindDto.getFollowUpTime());
        emailSenderQueue.setEmailParameter(clientEventRemindDto.getDescription());
        emailSenderQueue.setEmailTo(fkStaffId.toString());
        emailSenderQueueList.add(emailSenderQueue);
        Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);

    }

    @Override
    public List<BaseSelectEntity> getClientEventTypeSelect() {
        return studentEventTypeMapper.getClientEventTypeSelect();
    }
}
