package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LeaveSummaryVo {

    @ApiModelProperty(value = "员工Id")
    @JsonIgnore
    private Long staffId;

    @ApiModelProperty(value = "员工姓名")
    private String name;

    @ApiModelProperty(value = "工休单信息")
    private List<AiLeaveApplicationFormVo> leaveApplicationFormVos;

    @ApiModelProperty(value = "假期余额")
    private LeaveBalance leaveBalance;
}
