package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.query.AiExpenseQueryParam;
import com.get.financecenter.service.IAiService;
import com.get.financecenter.vo.ExpenseReimbursementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "Ai管理")
@RestController
@RequestMapping("finance/ai")
public class AiController {
    @Resource
    private IAiService aiService;

    // 查询自己的费用报销单和差旅报销单
    @ApiOperation(value = "查询自己的费用报销单和差旅报销单", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/Ai管理/查询自己的费用报销单和差旅报销单")
    @VerifyPermission(IsVerify = false)
    @PostMapping("getMyExpenseReimbursement")
    public ResponseBo<ExpenseReimbursementVo> getMyExpenseReimbursement( @RequestBody AiExpenseQueryParam aiExpenseQueryParam,
                                                                        @RequestHeader("at") String token) {
        return new ResponseBo<>(aiService.getMyExpenseReimbursement( aiExpenseQueryParam ));
    }

    //查询本人待处理的费用报销单和差旅报销单
    @ApiOperation(value = "查询需审批的费用报销单和差旅报销单", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/Ai管理/查询需审批的费用报销单和差旅报销单")
    @VerifyPermission(IsVerify = false)
    @PostMapping("getExpenseReimbursement")
    public ResponseBo<ExpenseReimbursementVo> getExpenseReimbursement(@RequestBody AiExpenseQueryParam aiExpenseQueryParam,@RequestHeader("at") String token) {
        return new ResponseBo<>(aiService.getExpenseReimbursement( aiExpenseQueryParam ));
    }

}
