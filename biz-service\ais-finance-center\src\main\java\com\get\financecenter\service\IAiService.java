package com.get.financecenter.service;

import com.get.financecenter.dto.query.AiExpenseQueryParam;
import com.get.financecenter.vo.ExpenseReimbursementVo;

public interface IAiService {
    /**
     * 查询自己的费用报销单和差旅报销单
     * @param startTime
     * @param endTime
     * @return
     */
    ExpenseReimbursementVo getMyExpenseReimbursement( AiExpenseQueryParam aiExpenseQueryParam );

    /**
     * 查询本人待处理的费用报销单和差旅报销单
     * @param startTime
     * @param endTime
     * @return
     */
    ExpenseReimbursementVo getExpenseReimbursement( AiExpenseQueryParam aiExpenseQueryParam );

}
