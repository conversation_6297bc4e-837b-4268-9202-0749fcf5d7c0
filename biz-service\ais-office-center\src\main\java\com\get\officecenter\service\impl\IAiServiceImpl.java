package com.get.officecenter.service.impl;

import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.service.IAiService;
import com.get.officecenter.service.ILeaveApplicationFormService;
import com.get.officecenter.vo.AiLeaveApplicationFormVo;
import com.get.officecenter.vo.LeaveBalance;
import com.get.officecenter.vo.LeaveSummaryVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service("officeAiService")
public class IAiServiceImpl implements IAiService {
    @Resource
    private ILeaveApplicationFormService leaveApplicationFormService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * 获取个人请假记录
     * selectStatus 0表示要显示我的申请列表，即我创建的表单
     *              1表示所有表单
     *              2表示显示我的审批列表，即我操作过的表单都要显示
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public LeaveSummaryVo getMyLeaveApplicationForms(String startTime, String endTime) {
        Long staffId = SecureUtil.getStaffInfo().getStaffId();
        LeaveSummaryVo expenseReimbursementVo = new LeaveSummaryVo();
        expenseReimbursementVo.setStaffId(staffId);
        String name = permissionCenterClient.getStaffName(staffId).getData();
        if (GeneralTool.isNotEmpty(name)) {
            expenseReimbursementVo.setName(name);
        }
        //获取请假记录
        List<AiLeaveApplicationFormVo> allLeaveApplicationForms = leaveApplicationFormService.getAllLeaveApplicationForms("0", startTime, endTime);
        //获取请假余额
        LeaveBalance leaveBalance = leaveApplicationFormService.holidayBalance(staffId);
        expenseReimbursementVo.setLeaveApplicationFormVos(allLeaveApplicationForms);
        if (GeneralTool.isNotEmpty(leaveBalance)) {
            expenseReimbursementVo.setLeaveBalance(leaveBalance);
        }
        return expenseReimbursementVo;
    }

    /**
     * 获取审批请假记录
     * selectStatus 0表示要显示我的申请列表，即我创建的表单
     *              1表示所有表单
     *              2表示显示我的审批列表，即我操作过的表单都要显示
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public LeaveSummaryVo getApprovalLeaveApplicationForms(String startTime, String endTime) {
        Long staffId = SecureUtil.getStaffInfo().getStaffId();
        LeaveSummaryVo expenseReimbursementVo = new LeaveSummaryVo();
        expenseReimbursementVo.setStaffId(staffId);
        String name = permissionCenterClient.getStaffName(staffId).getData();
        if (GeneralTool.isNotEmpty(name)) {
            expenseReimbursementVo.setName(name);
        }
        //获取请假记录
        List<AiLeaveApplicationFormVo> allLeaveApplicationForms = leaveApplicationFormService.getAllLeaveApplicationForms("2", startTime, endTime);
        expenseReimbursementVo.setLeaveApplicationFormVos(allLeaveApplicationForms);
        return expenseReimbursementVo;
    }

    /**
     * 获取所有请假记录
     * selectStatus 0表示要显示我的申请列表，即我创建的表单
     *              1表示所有表单
     *              2表示显示我的审批列表，即我操作过的表单都要显示
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public LeaveSummaryVo getAllLeaveApplicationForms(String startTime, String endTime) {
        LeaveSummaryVo expenseReimbursementVo = new LeaveSummaryVo();
        //获取请假记录
        List<AiLeaveApplicationFormVo> allLeaveApplicationForms = leaveApplicationFormService.getAllLeaveApplicationForms("1", startTime, endTime);
        expenseReimbursementVo.setLeaveApplicationFormVos(allLeaveApplicationForms);
        return expenseReimbursementVo;

    }


}
