package com.get.financecenter.service.impl;

import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.query.AiExpenseQueryParam;
import com.get.financecenter.service.IAiService;
import com.get.financecenter.service.IExpenseClaimFormService;
import com.get.financecenter.service.TravelClaimFormService;
import com.get.financecenter.vo.AiExpenseClaimFormVo;
import com.get.financecenter.vo.AiTravelClaimFormVo;
import com.get.financecenter.vo.ExpenseReimbursementVo;
import com.get.financecenter.vo.ReimbursementDetailVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class AiServiceImpl implements IAiService {
    @Resource
    private TravelClaimFormService travelClaimFormService;
    @Resource
    private IExpenseClaimFormService expenseClaimFormService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * 根据token 查询自己的费用报销单和差旅报销单
     * selectStatus 0表示要显示我的申请列表，即我创建的表单
     *              1表示所有表单
     *              2表示显示我的审批列表，即我操作过的表单都要显示
     * @return
     */
    @Override
    public ExpenseReimbursementVo getMyExpenseReimbursement(AiExpenseQueryParam aiExpenseQueryParam) {
        Long staffId = SecureUtil.getStaffInfo().getStaffId();
        ExpenseReimbursementVo expenseReimbursementVo = new ExpenseReimbursementVo();
        String name = permissionCenterClient.getStaffName(staffId).getData();
        if (GeneralTool.isNotEmpty(name)) {
            expenseReimbursementVo.setName(name);
        }

        List<AiTravelClaimFormVo> travelClaimFormVos = travelClaimFormService.getAllTravelClaimForms("0", aiExpenseQueryParam );
        List<AiExpenseClaimFormVo> expenseClaimFormVos = expenseClaimFormService.getAllExpenseClaimForms("0", aiExpenseQueryParam );
        ReimbursementDetailVo reimbursementDetailVo = new ReimbursementDetailVo();
        //差旅报销单
        expenseReimbursementVo.setReimbursementDetailVo(reimbursementDetailVo);
        if (GeneralTool.isNotEmpty(travelClaimFormVos)) {
            reimbursementDetailVo.setTravelClaimFormVos(travelClaimFormVos);
        }
        //费用报销单
        if (GeneralTool.isNotEmpty(expenseClaimFormVos)) {
            reimbursementDetailVo.setExpenseClaimFormVos(expenseClaimFormVos);
        }
//        expenseReimbursementVo.setStaffId(staffId);
        return expenseReimbursementVo;
    }

    /**
     * 查询需审批的费用报销单和差旅报销单
     * selectStatus 0表示要显示我的申请列表，即我创建的表单
     *              1表示所有表单
     *              2表示显示我的审批列表，即我操作过的表单都要显示
     * @return
     */

    @Override
    public ExpenseReimbursementVo getExpenseReimbursement(AiExpenseQueryParam aiExpenseQueryParam) {
        Long staffId = SecureUtil.getStaffInfo().getStaffId();
        ExpenseReimbursementVo expenseReimbursementVo = new ExpenseReimbursementVo();
        String name = permissionCenterClient.getStaffName(staffId).getData();
        if (name != null) {
            expenseReimbursementVo.setName(name);
        }

        List<AiTravelClaimFormVo> travelClaimFormVos = travelClaimFormService.getAllTravelClaimForms("2", aiExpenseQueryParam );

        List<AiExpenseClaimFormVo> expenseClaimFormVos = expenseClaimFormService.getAllExpenseClaimForms("2", aiExpenseQueryParam );
        ReimbursementDetailVo reimbursementDetailVo = new ReimbursementDetailVo();
        //差旅报销单
        expenseReimbursementVo.setReimbursementDetailVo(reimbursementDetailVo);
        if (GeneralTool.isNotEmpty(travelClaimFormVos)) {
            reimbursementDetailVo.setTravelClaimFormVos(travelClaimFormVos);
        }
        //费用报销单
        if (GeneralTool.isNotEmpty(expenseClaimFormVos)) {
            reimbursementDetailVo.setExpenseClaimFormVos(expenseClaimFormVos);
        }
//        expenseReimbursementVo.setStaffId(staffId);
        return expenseReimbursementVo;
    }

}
