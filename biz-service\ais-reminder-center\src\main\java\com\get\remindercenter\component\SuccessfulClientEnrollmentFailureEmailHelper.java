package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.SuccessfulClientEnrollmentFailureDto;
import com.get.remindercenter.dto.SystemSendEmailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;


@Component("successfulClientEnrollmentFailureEmailHelper")
@Slf4j
public class SuccessfulClientEnrollmentFailureEmailHelper extends EmailAbstractHelper{

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Resource
    private ISaleCenterClient saleCenterClient;


    @Resource
    private IInstitutionCenterClient institutionCenterClient;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            SuccessfulClientEnrollmentFailureDto successfulClientEnrollmentFailureDto = assembleEmailData(emailSenderQueue);

            EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
            emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
            emailSystemMQMessageDto.setTitle(emailSenderQueue.getEmailTitle());

            StringJoiner emailsCombined = new StringJoiner(", ");
            List<StaffVo> staff = permissionCenterClient.getStaffByIds(successfulClientEnrollmentFailureDto.getStaffEmailSet());
            Map<Long, StaffVo> data = staff.stream()
                    .collect(Collectors.toMap(
                            StaffVo::getId,  // Key: StaffVo 的 ID
                            staffVo -> staffVo  // Value: StaffVo 本身
                    ));
            for (Long id : successfulClientEnrollmentFailureDto.getStaffEmailSet()){
                try {
                    String template = setEmailTemplate(successfulClientEnrollmentFailureDto);
                    emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                    emailSystemMQMessageDto.setTitle(successfulClientEnrollmentFailureDto.getEmailTitle());
                    emailSystemMQMessageDto.setContent(template);
                    emailSystemMQMessageDto.setToEmail(data.get(id).getEmail());
                    //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(data.get(id).getEmail())) {
                        emailsCombined.add(data.get(id).getEmail());
                    }
                } catch (Exception e) {
                    // 记录发送失败的邮箱
                    String failedEmail = data.get(id) != null &&data.get(id).getEmail() != null ? data.get(id).getEmail() : "staffId:" + id;
                    failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}",  id, failedEmail, e.getMessage());
                }
            }
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }catch (Exception e){
            log.error("systemSendEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public SuccessfulClientEnrollmentFailureDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        SuccessfulClientEnrollmentFailureDto reminderDto = new SuccessfulClientEnrollmentFailureDto();
        BeanUtils.copyProperties(emailSenderQueue,reminderDto);
        Map<String, String> map = new HashMap<>();
        //获取申请计划
        StudentOfferItem studentOfferItem =saleCenterClient.getStudentOfferItemById(emailSenderQueue.getFkTableId()).getData();
        //获取学生信息
        Student student = saleCenterClient.getStudentById(studentOfferItem.getFkStudentId()).getData();
        //获取适用国家
        Result<AreaCountry> country = institutionCenterClient.getCountryById(studentOfferItem.getFkAreaCountryId());
        //获取学校信息
        Result<Institution> institution = institutionCenterClient.getInstitutionById(studentOfferItem.getFkInstitutionId());
        //获取课程信息
        Result<InstitutionCourseVo> course = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId());
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(student.getFkCompanyId());
        if(GeneralTool.isEmpty(versionValue2)){
            versionValue2 = "zh";
        }
        //学生名
        String studentName = new String();
        //申请国家
        String fkAreaCountryName = new String();
        //申请学校
        String fkInstitutionName = new String();
        //申请课程
        String fkCourseName = new String();
        String openingTime = "";
        SimpleDateFormat openingTimeFormat = new SimpleDateFormat("yyyy/MM/dd");
        if (GeneralTool.isNotEmpty(studentOfferItem.getDeferOpeningTime())){
            openingTime = openingTimeFormat.format(studentOfferItem.getDeferOpeningTime());
        }else {
            openingTime = openingTimeFormat.format(studentOfferItem.getOpeningTime());
        }

        String taskTitle = "";

        if (versionValue2.equals("en")) {
            studentName = student.getName();
            if (country.isSuccess() && GeneralTool.isNotEmpty(country.getData())) {
                fkAreaCountryName = country.getData().getName();
            }
            if (institution.isSuccess() && GeneralTool.isNotEmpty(institution.getData())) {
                fkInstitutionName = institution.getData().getName();
            }

            if (studentOfferItem.getFkInstitutionCourseId() != -1) {
                if (course.isSuccess() && GeneralTool.isNotEmpty(course.getData())) {
                    fkCourseName = course.getData().getName();
                }
            } else {
                fkCourseName = studentOfferItem.getOldCourseCustomName();
            }
            taskTitle = "Set up enrollment failure notifications for the list of successful customers, students:"+studentName+"，country:"+fkAreaCountryName;
        }else {
            studentName = student.getName() + "（" + student.getLastName() + "  " + student.getFirstName() + "）";

            if (GeneralTool.isNotEmpty(country.getData().getNameChn())) {
                fkAreaCountryName = country.getData().getName() + "(" + country.getData().getNameChn() + ")";
            } else {
                fkAreaCountryName = country.getData().getName();
            }
            taskTitle = "成功客户列表设置入学失败通知，学生："+studentName+"，国家："+fkAreaCountryName;
            if (institution.isSuccess() && GeneralTool.isNotEmpty(institution.getData())) {
                if (GeneralTool.isNotEmpty(institution.getData().getNameChn())) {
                    fkInstitutionName = institution.getData().getName() + "(" + institution.getData().getNameChn() + ")";
                } else {
                    fkInstitutionName = institution.getData().getName();
                }
            }
            if (studentOfferItem.getFkInstitutionCourseId() != -1) {
                if (course.isSuccess() && GeneralTool.isNotEmpty(course.getData())) {
                    fkCourseName = course.getData().getName() + "(" + course.getData().getNameChn() + ")";
                }
            } else {
                fkCourseName = studentOfferItem.getOldCourseCustomName();
            }
        }

        String emailTo = emailSenderQueue.getEmailTo();
        if (GeneralTool.isNotEmpty(emailTo)) {
            String cleanedEmailTo = emailTo.replaceAll("[\\[\\]]", "");
            String[] emailToArray = cleanedEmailTo.split("\\s*,\\s*");
            Set<Long> list = new HashSet<>();
            for (String staffId : emailToArray) {
                list.add(Long.valueOf(staffId));
            }
            reminderDto.setStaffEmailSet(list);
        }



        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> parsedMap = null;
        String emailParameter = emailSenderQueue.getEmailParameter();
        try {
            parsedMap = mapper.readValue(emailParameter, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        String operator =parsedMap.get("operator");
        String operatingTime =parsedMap.get("operatingTime");
        String enrolFailure =parsedMap.get("enrolFailure");
        map.put("studentName",studentName);
        map.put("fkAreaCountryName",fkAreaCountryName);
        map.put("fkInstitutionName",fkInstitutionName);
        map.put("fkCourseName",fkCourseName);
        map.put("openingTime",openingTime);
        map.put("operator",operator);
        map.put("operatingTime",operatingTime);
        map.put("enrolFailure",enrolFailure);
        reminderDto.setMap(map);
        reminderDto.setLanguageCode(versionValue2);
        reminderDto.setEmailTitle(taskTitle);

        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }


        return reminderDto;
    }


    private String setEmailTemplate(SuccessfulClientEnrollmentFailureDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();

        if (GeneralTool.isNotEmpty(reminderDto.getFkEmailTypeKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, reminderDto.getFkEmailTypeKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }
}
