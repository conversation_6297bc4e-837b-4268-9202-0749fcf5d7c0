package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.ClientEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.FocExportVo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.*;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.dto.StaffByIdsAndCompanyIdsDto;
import com.get.permissioncenter.entity.StaffConfig;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.*;
import com.get.salecenter.dto.*;
import com.get.salecenter.dto.query.StudentListQueryDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.utils.ConvertUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.sale.VerifyStudentOfferItemUtils;
import com.get.salecenter.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

/**
 * author:Neil
 * Time: 12:54
 * Date: 2022/8/17
 * Description:售前客户实现类
 */
@Service
@Slf4j
public class ClientServiceImpl implements IClientService {



    @Resource
    private IPermissionCenterClient permissionCenterClient;


    @Resource
    private ClientMapper clientMapper;

    @Resource
    private StudentMapper studentMapper;

    @Resource
    private StudentEducationLevelTypeMapper studentEducationLevelTypeMapper;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private StudentProjectRoleStaffMapper studentProjectRoleStaffMapper;

    @Resource
    private IStudentProjectRoleStaffService projectRoleStaffService;

    @Resource
    private UtilService<Object> utilService;

    @Resource
    private ClientEventMapper clientEventMapper;
    @Resource
    private StudentServiceFeeMapper studentServiceFeeMapper;

    @Resource
    private IAgentService agentService;

    @Resource
    private ClientAgentMapper clientAgentMapper;

    @Resource
    private ClientOfferMapper clientOfferMapper;

    @Resource
    private IClientAgentService clientAgentService;

    @Resource
    private IContactPersonService contactPersonService;

    @Resource
    private IDeleteService deleteService;

    @Resource
    private IMediaAndAttachedService attachedService;

    @Resource
    private ICommentService commentService;

    @Resource
    private RStudentToClientApprovalMapper rStudentToClientApprovalMapper;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private IStudentAgentService studentAgentService;

    @Resource
    private VerifyStudentOfferItemUtils verifyStudentOfferItemUtils;

    @Resource
    private StudentOfferItemMapper studentOfferItemMapper;

    @Resource
    private ClientSourceMapper clientSourceMapper;

    @Resource
    private AgentStaffMapper agentStaffMapper;

    @Resource
    private AsyncExportService asyncExportService;

    @Resource
    private ClientOfferStepMapper clientOfferStepMapper;

    @Resource
    private BusinessProviderMapper businessProviderMapper;

    @Resource
    private StudentEventTypeMapper studentEventTypeMapper;
    @Resource
    private AgentCompanyMapper agentCompanyMapper;
    @Resource
    private StudentAgentMapper studentAgentMapper;

    @Resource
    private IStudentService studentService;

    @Resource
    private ClientStaffMapper clientStaffMapper;

    @Resource
    private StudentOfferItemStepMapper studentOfferItemStepMapper;

    @Resource
    @Lazy
    private IStudentOfferService studentOfferService;

    @Resource
    private StudentOfferMapper studentOfferMapper;

    @Value("${domainName}")
    private String domainName;

    @Resource
    private ClientStaffService clientStaffService;

    private static Integer getAge(Date birthDay) {
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthDay)) {
            //出生日期晚于当前时间，无法计算
            return null;
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(birthDay);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        int age = yearNow - yearBirth;
        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;//当前日期在生日之前，年龄减一
                }
            } else {
                age--;//当前月份在生日之前，年龄减一
            }
        }
        return age;
    }


    /**
     * Author Cream
     * Description : // 获取售前资源列表
     * Date 2022/8/17 14:54
     * Params: clientDto
     * Return ResponseBo<ClientVo>
     */
    @Override
    public ResponseBo<ClientListInfoDto> getListOfPreSalesResources(ClientDto clientDto, Page page) {
        if (null == clientDto) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Long staffId = SecureUtil.getStaffId();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
        if (result.isSuccess()) {
            List<Long> followerIds = result.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            followerIds.add(staffId);
            IPage<Client> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            if (StringUtils.isNotBlank(clientDto.getName())) {
                clientDto.setName(clientDto.getName().replace(" ", "").trim().toLowerCase());
            }
            List<ClientListInfoVo> clientDtos = clientMapper.getResourceList(pages, clientDto, followerIds, false, true);
            if (clientDtos.isEmpty()) {
                return new ListResponseBo<>(Collections.emptyList());
            }
            page.setAll((int) pages.getTotal());
            List<ClientListInfoDto> list = new ArrayList<>(clientDtos.size());
            pack(clientDtos, clientDto, list);
            return new ListResponseBo<>(list, page);
        }
        return new ListResponseBo<>(Collections.emptyList());
    }

    private void pack(List<ClientListInfoVo> clientDtos, ClientDto clientDto1, List<ClientListInfoDto> list) {
        Set<Long> companyIds = clientDtos.stream().map(ClientListInfoVo::getFkCompanyId).collect(Collectors.toSet());
        Result<Map<Long, String>> companyNamesByIds = permissionCenterClient.getCompanyNamesByIds(companyIds);
        Map<Long, String> cMap = new HashMap<>(companyIds.size());
        if (companyNamesByIds.isSuccess() && companyNamesByIds.getData() != null) {
            cMap = companyNamesByIds.getData();
        }
        Set<Integer> stepOrders = clientDtos.stream().map(ClientListInfoVo::getMaxStepOrder).collect(Collectors.toSet());
        Map<Integer, ClientOfferStep> clientOfferStepMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(stepOrders)) {
            List<ClientOfferStep> clientOfferSteps = clientOfferStepMapper.selectList(Wrappers.lambdaQuery(ClientOfferStep.class)
                    .in(ClientOfferStep::getStepOrder, stepOrders));
            clientOfferStepMap = clientOfferSteps.stream().collect(Collectors.toMap(ClientOfferStep::getStepOrder, Function.identity()));
        }

        Set<Long> ids = clientDtos.stream().map(ClientListInfoVo::getId).collect(Collectors.toSet());

        //最近回访时间
        List<SelItem> visitTime = clientEventMapper.getLastVisitTime(ids);
        Map<Long, Object> convert = ConvertUtils.convert(visitTime);

        //预约回访时间
        List<SelItem> time = clientEventMapper.getVisitTime(ids, clientDto1.getBeginFollowUpTime(), clientDto1.getEndFollowUpTime());
        Map<Long, Object> c2 = ConvertUtils.convert(time);
        Map<Long, Object> projectRole = projectRoleStaffService.getAFollowUpConsultant(TableEnum.CLIENT_OFFER.key, ids, "GEA_RESOURCE_ARC");

        //院校名称
        Set<Long> institutionIds = Sets.newHashSet();
        Set<Long> fkInstitutionIdEducations = clientDtos.stream().map(ClientListInfoVo::getFkInstitutionIdEducation).collect(Collectors.toSet());
        Set<Long> fkInstitutionIdEducation2s = clientDtos.stream().map(ClientListInfoVo::getFkInstitutionIdEducation2).collect(Collectors.toSet());
        institutionIds.addAll(fkInstitutionIdEducations);
        institutionIds.addAll(fkInstitutionIdEducation2s);
        Map<Long, String> institutionNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds).getData();
        }
        //学生
        List<String> studentNumList = clientDtos.stream().filter(clientListInfoVo -> GeneralTool.isNotEmpty(clientListInfoVo.getStudentSource()) && clientListInfoVo.getStudentSource().startsWith("ST")).map(ClientListInfoVo::getStudentSource).collect(Collectors.toList());
        //学生编号 - 学生idMap
        Map<String, Long> studentNumMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(studentNumList)) {
            List<Student> students = studentMapper.selectList(Wrappers.lambdaQuery(Student.class)
                    .in(Student::getNum, studentNumList));
            students.forEach(student -> studentNumMap.put(student.getNum(), student.getId()));
        }

        Set<Long> businessProviderIds = clientDtos.stream().filter(dto -> ProjectKeyEnum.CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER.key.equals(dto.getFkTableName())).map(ClientListInfoVo::getFkTableId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> businessProviderNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(businessProviderIds)) {
            List<BusinessProvider> businessProviders = businessProviderMapper.selectBatchIds(businessProviderIds);
            if (GeneralTool.isNotEmpty(businessProviders)) {
                businessProviderNameMap = businessProviders.stream().collect(Collectors.toMap(BusinessProvider::getId, businessProvider -> {
                    if (GeneralTool.isNotEmpty(businessProvider.getName())) {
                        if (GeneralTool.isNotEmpty(businessProvider.getNameChn())) {
                            return businessProvider.getName() + "(" + businessProvider.getNameChn() + ")";
                        }
                        return businessProvider.getName();
                    } else if (GeneralTool.isNotEmpty(businessProvider.getNameChn())) {
                        return businessProvider.getNameChn();
                    }
                    return "";
                }));
            }
        }


//        Set<Long> countryIds = clientDtos.stream().map(ClientListInfoVo::getFkAreaCountryId).collect(Collectors.toSet());
//        Map<Long, String> countryNameIds = Maps.newHashMap();
//        if (GeneralTool.isNotEmpty(countryIds)) {
//            countryNameIds = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
//        }
        Set<Long> agentIds = Sets.newHashSet();
        Set<Long> clientAgentIds = clientDtos.stream().map(ClientListInfoVo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> sourceAgentIds = clientDtos.stream().map(ClientListInfoVo::getSourceAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        agentIds.addAll(clientAgentIds);
        agentIds.addAll(sourceAgentIds);
        Map<Long, String> agentNamesMap = Maps.newHashMap();
        Map<Long, String> bdNameMap = Maps.newHashMap();
        Map<Long, String> staffNamesByIds = Maps.newHashMap();
        Set<Long> staffIds = Sets.newHashSet();
        Set<Long> sourceStaffIds = clientDtos.stream().map(ClientListInfoVo::getSourceStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        staffIds.addAll(sourceStaffIds);
        if (GeneralTool.isNotEmpty(clientAgentIds)) {
            List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                    .in(AgentStaff::getFkAgentId, clientAgentIds)
                    .eq(AgentStaff::getIsActive, true));
            if (GeneralTool.isNotEmpty(agentStaffs)) {
                Set<Long> clientStaffIds = agentStaffs.stream().map(AgentStaff::getFkStaffId).collect(Collectors.toSet());
                staffIds.addAll(clientStaffIds);
                if (GeneralTool.isNotEmpty(staffIds)) {
                    staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
                }
                if (GeneralTool.isNotEmpty(clientStaffIds)) {
                    Map<Long, List<AgentStaff>> bdListMap = agentStaffs.stream().collect(Collectors.groupingBy(AgentStaff::getFkAgentId));
                    for (Map.Entry<Long, List<AgentStaff>> entry : bdListMap.entrySet()) {
                        AgentStaff agentStaff = entry.getValue().get(0);
                        Long fkStaffId = agentStaff.getFkStaffId();
                        String bdName = staffNamesByIds.get(fkStaffId);
                        if (GeneralTool.isNotEmpty(bdName)) {
                            bdNameMap.put(entry.getKey(), bdName);
                        }
                    }
                }
            }
        }
        if (GeneralTool.isNotEmpty(agentIds)) {
            agentNamesMap = agentService.getAgentNamesByIds(agentIds);
        }
        if (GeneralTool.isNotEmpty(staffIds) && GeneralTool.isEmpty(staffNamesByIds)) {
            staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
        }


        Set<Long> offerCountryIds = clientDtos.stream().map(ClientListInfoVo::getOfferCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(offerCountryIds)) {
            countryNameMap = institutionCenterClient.getCountryNamesByIds(offerCountryIds).getData();
        }

        //项目成员
        Map<Long, List<ClientProjectRoleStaffVo>> projectRoleStaffListMap = Maps.newHashMap();
        Map<Long, String> projectStaffNamesByIds = Maps.newHashMap();
        List<Long> clientIds = clientDtos.stream().map(ClientListInfoVo::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ClientProjectRoleStaffVo> clientProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaffByClientIds(clientIds);
        if (GeneralTool.isNotEmpty(clientProjectRoleStaffVos)) {
            projectRoleStaffListMap = clientProjectRoleStaffVos.stream().collect(Collectors.groupingBy(ClientProjectRoleStaffVo::getFkTableId));
            Set<Long> clientStaffIds = clientProjectRoleStaffVos.stream().map(ClientProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(clientStaffIds)) {
                projectStaffNamesByIds = permissionCenterClient.getStaffNamesByIds(clientStaffIds);
            }
        }

        List<ClientEvent> clientEvents = clientEventMapper.selectList(Wrappers.lambdaQuery(ClientEvent.class)
                .in(ClientEvent::getFkClientId, ids)
                .ne(ClientEvent::getFkStudentEventTypeId, 5L)
        );
        Map<Long, List<ClientEvent>> clientEventListMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(clientEvents)) {
            clientEventListMap = clientEvents.stream().collect(Collectors.groupingBy(ClientEvent::getFkClientId));
        }

        List<StudentEventType> studentEventTypes = studentEventTypeMapper.selectList(null);
        Map<Long, String> studentEventTypeMap = studentEventTypes.stream().collect(Collectors.toMap(StudentEventType::getId, StudentEventType::getTypeName));


        for (ClientListInfoVo clientDto : clientDtos) {
            ClientListInfoDto listInfoVo = new ClientListInfoDto();
            BeanUtils.copyProperties(clientDto, listInfoVo);

            listInfoVo.setFkAgentName(clientDto.getAgentName());
            Object o = convert.get(clientDto.getId());
            if (o != null) {
                listInfoVo.setLastVisitTime((Date) o);
            }
            Object o1 = c2.get(clientDto.getId());
            if (o1 != null) {
                listInfoVo.setFollowUpTime((Date) o1);
            }
            listInfoVo.setRemark(clientDto.getOfferRemark());
            listInfoVo.setFkCompanyName(cMap.get(clientDto.getFkCompanyId()));
            if (projectRole.containsKey(clientDto.getOfferId())) {
                listInfoVo.setProjectRoleName((String) projectRole.get(clientDto.getOfferId()));
            }
//            listInfoVo.setFollowUpStatus(ClientEnum.getValueByKey(clientDto.getStatus(), ClientEnum.FUS));

            //入读/毕业院校
            String institutionNameEducation = "";
            String institutionNameEducation2 = "";
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionIdEducation()) && GeneralTool.isNotEmpty(institutionNameMap.get(clientDto.getFkInstitutionIdEducation()))) {
                institutionNameEducation = institutionNameMap.get(clientDto.getFkInstitutionIdEducation());
            }
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionNameEducation())) {
                institutionNameEducation = clientDto.getFkInstitutionNameEducation();
            }
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionIdEducation2()) && GeneralTool.isNotEmpty(institutionNameMap.get(clientDto.getFkInstitutionIdEducation2()))) {
                institutionNameEducation2 = institutionNameMap.get(clientDto.getFkInstitutionIdEducation2());
            }
            if (GeneralTool.isNotEmpty(clientDto.getFkInstitutionNameEducation2())) {
                institutionNameEducation2 = clientDto.getFkInstitutionNameEducation2();
            }
            String institutionName = "";
            if (GeneralTool.isNotEmpty(institutionNameEducation)) {
                institutionName = "国内：" + institutionNameEducation;
            }
            if (GeneralTool.isNotEmpty(institutionNameEducation2)) {
                if (GeneralTool.isNotEmpty(institutionName)) {
                    institutionName = institutionName + "<br>";
                }
                institutionName = institutionName + "国际：" + institutionNameEducation2;
            }
            if (GeneralTool.isNotEmpty(institutionName)) {
                listInfoVo.setInstitutionName(institutionName);
            }

            //入读/毕业专业
            String educationMajor = (GeneralTool.isNotEmpty(clientDto.getEducationMajor()) ? clientDto.getEducationMajor() : "");
            String educationMajor2 = (GeneralTool.isNotEmpty(clientDto.getEducationMajor2()) ? clientDto.getEducationMajor2() : "");
            String educationMajorFullName = "";
            if (GeneralTool.isNotEmpty(educationMajor)) {
                educationMajorFullName = "国内：" + educationMajor;
            }
            if (GeneralTool.isNotEmpty(educationMajor2)) {
                if (GeneralTool.isNotEmpty(educationMajorFullName)) {
                    educationMajorFullName = educationMajorFullName + "<br>";
                }
                educationMajorFullName = educationMajorFullName + "国际：" + educationMajor2;
            }
            if (GeneralTool.isNotEmpty(educationMajorFullName)) {
                listInfoVo.setMajorName(educationMajorFullName);
            }


            //入读时间
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            String startTimeEducation = GeneralTool.isNotEmpty(clientDto.getStartTimeEducation()) ? sf.format(clientDto.getStartTimeEducation()) : "";
            String startTimeEducation2 = GeneralTool.isNotEmpty(clientDto.getStartTimeEducation2()) ? sf.format(clientDto.getStartTimeEducation2()) : "";
            String startTimeEducationFullName = "";
            if (GeneralTool.isNotEmpty(startTimeEducation)) {
                startTimeEducationFullName = "国内：" + startTimeEducation;
            }
            if (GeneralTool.isNotEmpty(startTimeEducation2)) {
                if (GeneralTool.isNotEmpty(startTimeEducationFullName)) {
                    startTimeEducationFullName = startTimeEducationFullName + "<br>";
                }
                startTimeEducationFullName = startTimeEducationFullName + "国际：" + startTimeEducation2;
            }
            if (GeneralTool.isNotEmpty(startTimeEducationFullName)) {
                listInfoVo.setStartTimeEducation(startTimeEducationFullName);
            }


            //毕业时间
            String endTimeEducation = GeneralTool.isNotEmpty(clientDto.getEndTimeEducation()) ? sf.format(clientDto.getEndTimeEducation()) : "";
            String endTimeEducation2 = GeneralTool.isNotEmpty(clientDto.getEndTimeEducation2()) ? sf.format(clientDto.getEndTimeEducation2()) : "";
            String endTimeEducationFullName = "";
            if (GeneralTool.isNotEmpty(endTimeEducation)) {
                endTimeEducationFullName = "国内：" + endTimeEducation;
            }
            if (GeneralTool.isNotEmpty(endTimeEducation2)) {
                if (GeneralTool.isNotEmpty(endTimeEducationFullName)) {
                    endTimeEducationFullName = endTimeEducationFullName + "<br>";
                }
                endTimeEducationFullName = endTimeEducationFullName + "国际：" + endTimeEducation2;
            }
            if (GeneralTool.isNotEmpty(endTimeEducationFullName)) {
                listInfoVo.setEndTimeEducation(endTimeEducationFullName);
            }


            //是否入境
            if (GeneralTool.isNotEmpty(clientDto.getIsEnterCountry())) {
                listInfoVo.setIsEnterCountryName(clientDto.getIsEnterCountry() ? "是" : "否");
            } else {
                listInfoVo.setIsEnterCountryName("否");
            }

            //bd
            listInfoVo.setBdName(bdNameMap.get(clientDto.getFkAgentId()));

            //项目成员
            List<ClientProjectRoleStaffVo> projectRoleStaffDtos = projectRoleStaffListMap.get(clientDto.getOfferId());
            StringBuilder sb = new StringBuilder();
            if (GeneralTool.isNotEmpty(projectRoleStaffDtos)) {
                for (ClientProjectRoleStaffVo projectRoleStaffDto : projectRoleStaffDtos) {
                    if (sb.toString().contains("：")) {
                        sb.append("<br>");
                    }
                    sb.append(projectRoleStaffDto.getRoleName()).append("：");
                    if (GeneralTool.isNotEmpty(projectRoleStaffDto.getFkStaffId()) && GeneralTool.isNotEmpty(projectStaffNamesByIds.get(projectRoleStaffDto.getFkStaffId()))) {
                        sb.append(projectStaffNamesByIds.get(projectRoleStaffDto.getFkStaffId()));
                    }
                }
            }
            listInfoVo.setProjectRoleName(sb.toString());

//            listInfoVo.setFkAreaCountryName(countryNameIds.get(clientDto.getFkAreaCountryId()));

            if (GeneralTool.isNotEmpty(clientDto.getStudentSource())) {
                listInfoVo.setIsBmsStudent(clientDto.getStudentSource().startsWith("ST"));
                if (clientDto.getStudentSource().startsWith("ST")) {
                    listInfoVo.setFkStudentId(studentNumMap.get(clientDto.getStudentSource()));
                }
            } else {
                listInfoVo.setIsBmsStudent(false);
            }

            if (GeneralTool.isNotEmpty(clientDto.getMaxStepOrder())) {
                ClientOfferStep clientOfferStep = clientOfferStepMap.get(clientDto.getMaxStepOrder());
                if (GeneralTool.isNotEmpty(clientOfferStep)) {
                    listInfoVo.setFkClientOfferStepName(clientOfferStep.getStepName());
                }

            }
            if (GeneralTool.isNotEmpty(clientDto.getFkAgentId())) {
                listInfoVo.setFkAgentName(agentNamesMap.get(clientDto.getFkAgentId()));
            } else if (GeneralTool.isNotEmpty(clientDto.getAgentName())) {
                listInfoVo.setFkAgentName(clientDto.getAgentName());
            }

            if (GeneralTool.isNotEmpty(clientDto.getOfferCountryId())) {
                listInfoVo.setOfferCountryName(countryNameMap.get(clientDto.getOfferCountryId()));
            }

            if (GeneralTool.isNotEmpty(clientDto.getSourceStaffId())) {
                listInfoVo.setSourceBdName(staffNamesByIds.get(clientDto.getSourceStaffId()));
            }

            if (GeneralTool.isNotEmpty(clientDto.getSourceAgentId())) {
                listInfoVo.setSourceAgentName(agentNamesMap.get(clientDto.getSourceAgentId()));
            }

            if (ProjectKeyEnum.CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER.key.equals(clientDto.getFkTableName())) {
                listInfoVo.setSourceBusinessProviderName(businessProviderNameMap.get(clientDto.getFkTableId()));
            }

            List<ClientEvent> events = clientEventListMap.get(listInfoVo.getId());
            if (GeneralTool.isNotEmpty(events)) {
                Optional<ClientEvent> max = events.stream().max(Comparator.comparing(ClientEvent::getGmtCreate));
                max.ifPresent(event -> listInfoVo.setLatestEvent(studentEventTypeMap.get(event.getFkStudentEventTypeId())
                        + (GeneralTool.isNotEmpty(event.getDescription()) ? ":" + event.getDescription() : "")));
            }

            // 翻译推荐来源类型名称
            if (GeneralTool.isNotEmpty(clientDto.getFkTableName())) {
                listInfoVo.setFkTableNameValue(ProjectKeyEnum.getInitialValue(clientDto.getFkTableName()));
            }

            //负责人名称
            if (GeneralTool.isNotEmpty(clientDto.getFkStudentStaffIds())){
                //
            }

            list.add(listInfoVo);
        }
    }

    /**
     * Author Cream
     * Description : //导出售前资源列表
     * Date 2022/9/15 10:44
     * Params:
     * Return
     */
    @Override
    public void clientResourcesExport(ClientDto clientDto, List<FocExportVo> focExportVos, HttpServletResponse response) {


        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        String locale = SecureUtil.getLocale();

        asyncExportService.exportClientResources(clientDto, focExportVos, headerMap, staffInfo, locale);


    }

    /**
     * Author Cream
     * Description : // 跟进状态下拉
     * Date 2022/8/17 15:37
     * Params:
     * Return   Map<String, Object>
     */
    @Override
    public ResponseBo<Map<String, Object>> getFollowUpStatusList() {
        return new ListResponseBo<>(ClientEnum.enumsTranslation2Arrays(ClientEnum.FUS));
    }


    /**
     * 添加售前资源客户
     *
     * @param clientAddDto
     * @return
     */
    private static final int IS_ACTIVE = 1;
    private static final int IS_NOT_ACTIVE = 0;


    @Override
    public Long addClient(ClientAddDto clientAddDto) {
        if (GeneralTool.isEmpty(clientAddDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Client client = BeanCopyUtils.objClone(clientAddDto, Client::new);
        client.setIsActive(true);
        utilService.setCreateInfo(client);
        clientMapper.insert(client);
        // 添加资源客户负责人
        ClientStaffVo clientStaffVo = new ClientStaffVo();
        clientStaffVo.setFkClientId(client.getId());
        utilService.setCreateInfo(clientStaffVo);
        clientStaffVo.setFkStaffId(SecureUtil.getStaffId());
        clientStaffVo.setIsActive(IS_ACTIVE);
        clientStaffVo.setActiveDate(new Date());
        clientStaffMapper.insert(clientStaffVo);

        client.setNum(GetStringUtils.getClientNum(client.getId()));
        clientMapper.updateById(client);
        sendVisaExpiresEmail(client);
        return client.getId();
    }

    /**
     * 修改售前资源客户
     *
     * @param clientUpdateVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClientVo updateClient(ClientUpdateDto clientUpdateVo) {
        if (GeneralTool.isEmpty(clientUpdateVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Client client = clientMapper.selectById(clientUpdateVo.getId());
        if (GeneralTool.isEmpty(client)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Client clientSave = BeanCopyUtils.objClone(clientUpdateVo, Client::new);
        utilService.updateUserInfoToEntity(clientSave);
        clientMapper.updateByIdWithNull(clientSave);

        // 如果签证到期字段有修改，则删除以前未发送的任务，并且新增新的发送任务(触发更新逻辑)
        if (judeDateIsUpdate(clientUpdateVo.getVisaExpiryDate(), client.getVisaExpiryDate())) {
            // 删除以前的待发送邮件
            List<String> list = Arrays.asList(ProjectKeyEnum.STUDENT_RESOURCE_EXPIRED_NOTICE.key);
            //reminderCenterClient.batchDeleteByTableId(TableEnum.SALE_CLIENT.key, client.getId(), list);
            List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
            RemindTaskDto remindTaskDto = new RemindTaskDto();
            remindTaskDto.setFkTableName(TableEnum.SALE_CLIENT.key);
            remindTaskDto.setFkTableId(client.getId());
            remindTaskDto.setFkRemindEventTypeKey(ProjectKeyEnum.STUDENT_RESOURCE_EXPIRED_NOTICE.key);
            remindTaskDtos.add(remindTaskDto);
            reminderCenterClient.deleteEmailQueueByTableId(remindTaskDtos);
            // 发邮件
            if (GeneralTool.isNotEmpty(clientUpdateVo.getVisaExpiryDate())) {
                Client newClient = BeanCopyUtils.objClone(clientUpdateVo, Client::new);
                sendVisaExpiresEmail(newClient);
            }
        }

        return findClientById(client.getId());
    }

    private Boolean judeDateIsUpdate(Date visaExpiryDate, Date oldVisaExpiryDate) {
        if (GeneralTool.isEmpty(visaExpiryDate) && GeneralTool.isEmpty(oldVisaExpiryDate))
            return false;
        if (GeneralTool.isNotEmpty(visaExpiryDate) && GeneralTool.isNotEmpty(oldVisaExpiryDate)) {
            return !visaExpiryDate.equals(oldVisaExpiryDate);
        }
        return true;
    }

    /**
     * 根据签字日期，设置发送签证到期预警邮件
     *
     * @param
     * @return
     */
    private Boolean sendVisaExpiresEmail(Client client) {
        if (GeneralTool.isEmpty(client.getVisaExpiryDate()))
            return false;
        // 如果签证到期日期不为空，则发送预警邮件
        // 查询系统配置，获取提前的时间 和 通知的staffId
        CompanyConfigAnalysisVo analysisDto = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.REMINDER_EMAIL_CLIENT.key).getData().get(SecureUtil.getFkCompanyId());
        String value2Json = analysisDto.getValue2();
        if (GeneralTool.isEmpty(value2Json))
            log.error("系统配置为空");
        JSONObject jsonObject = JSONObject.parseObject(value2Json);
        com.alibaba.fastjson.JSONArray notifier = jsonObject.getJSONArray("Notifier");
        Integer advanceDays = jsonObject.getInteger("Days");
        List<Long> staffIds = notifier.stream()
                .map(obj -> (Integer) obj).map(i -> Long.valueOf(i))  // 将每个对象转换为 Integer
                .collect(Collectors.toList());
        if (GeneralTool.isEmpty(staffIds) || GeneralTool.isEmpty(advanceDays))
            return false;
        List<RemindTaskDto> remindTaskVos = new ArrayList();
        Map<String, String> map = new HashMap<>();
        map.put("visaExpirationDateStr", DateUtil.format(client.getVisaExpiryDate(), "yyyy-MM-dd"));
        map.put("name", client.getName());
        map.put("gmtCreateUser", client.getGmtCreateUser());
        map.put("gmtCreate", DateUtil.format(client.getGmtCreate(), "yyyy-MM-dd"));


        // 根据staffIds发送预警邮件
        for (Long staffId : staffIds) {
            String staffName = permissionCenterClient.getStaffName(staffId).getData();
            RemindTaskDto remindTaskVo = new RemindTaskDto();
            //邮件方式发送
            remindTaskVo.setRemindMethod("1");
            //默认设置执行中
            remindTaskVo.setStatus(1);
            //默认背景颜色
            remindTaskVo.setTaskBgColor("#3788d8");
            //TODO STUDENT_RESOURCE_EXPIRED_NOTICE
            remindTaskVo.setTaskTitle("【学生资源签证到期日提醒】" + client.getName() + "签证到期日" + client.getVisaExpiryDate());
            remindTaskVo.setTaskRemark(MyStringUtils.getReminderTemplate(map, SaleCenterConstant.STUDENT_RESOURCE_EXPIRE_REMINDER));
            remindTaskVo.setFkStaffId(staffId);
            remindTaskVo.setStartTime(GetDateUtil.getAdvanceDateByDay(client.getVisaExpiryDate(), advanceDays));
            remindTaskVo.setAdvanceDays(advanceDays.toString());
            remindTaskVo.setFkTableName(TableEnum.SALE_CLIENT.key);
            remindTaskVo.setFkTableId(client.getId());
            remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.STUDENT_RESOURCE_EXPIRED_NOTICE.key);
//            remindTaskVo.setTaskLink(domainName + "/sales-center_repository-management_repository-detail/" + client.getId() + "?clientId=" + client.getId());
            remindTaskVos.add(remindTaskVo);
        }
        //return reminderCenterClient.batchAdd(remindTaskVos).getData();
        reminderCenterClient.batchDeleteTaskNew(remindTaskVos);
        List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
        emailSenderQueue.setFkTableId(client.getId());
        emailSenderQueue.setFkTableName(TableEnum.SALE_CLIENT.key);
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.STUDENT_RESOURCE_EXPIRED_NOTICE.getEmailTemplateKey());
        emailSenderQueue.adjustOpTime(client.getVisaExpiryDate(), advanceDays);
        emailSenderQueue.setEmailParameter(JSONObject.toJSONString(map));
        emailSenderQueue.setEmailTo(staffIds.toString());
        emailSenderQueueList.add(emailSenderQueue);
        Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        if (!booleanResult.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(booleanResult.getMessage()));
        }
        return booleanResult.getData();
    }

    @Override
    public ClientVo findClientById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        verifyDataPermissionsUtils.verifyByBusinessId(id, VerifyDataPermissionsUtils.STUDENT_O);
        Client client = clientMapper.selectById(id);
        if (GeneralTool.isNotEmpty(client)) {
            if (!SecureUtil.validateCompany(client.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        } else {
            return null;//找不到就返回
        }
        ClientVo clientVo = BeanCopyUtils.objClone(client, ClientVo::new);
        String educationLevelType = clientVo.getEducationLevelType();
        String educationLevelType2 = clientVo.getEducationLevelType2();
        List<StudentEducationLevelType> studentEducationLevelTypes = studentEducationLevelTypeMapper.selectList(Wrappers.lambdaQuery());
        Map<Long, String> educationMap = studentEducationLevelTypes.stream().collect(Collectors.toMap(StudentEducationLevelType::getId, StudentEducationLevelType::getTypeNameChn));
        if (GeneralTool.isNotEmpty(educationLevelType) && StringUtils.isNumeric(educationLevelType)) {
            clientVo.setDomesticEducationName(educationMap.get(Long.valueOf(educationLevelType)));
        }
        if (GeneralTool.isNotEmpty(educationLevelType2) && StringUtils.isNumeric(educationLevelType2)) {
            clientVo.setInternationalEducationName(educationMap.get(Long.valueOf(educationLevelType2)));
        }

        Map<String, String> companyMap = getCompanyMap();

        Set<Long> countryIds = new HashSet<>();
        countryIds.add(clientVo.getFkAreaCountryId());
        countryIds.add(clientVo.getFkAreaCountryIdNationality());
        countryIds.add(clientVo.getFkAreaCountryIdGreenCard());
        countryIds.add(clientVo.getFkAreaCountryIdEducation());
        countryIds.add(clientVo.getFkAreaCountryIdEducation2());
        countryIds.add(clientVo.getFkAreaCountryIdBirth());

        Set<Long> stateIds = new HashSet<>();
        stateIds.add(clientVo.getFkAreaStateId());
        stateIds.add(clientVo.getFkAreaStateIdEducation());
        stateIds.add(clientVo.getFkAreaStateIdEducation2());
        stateIds.add(clientVo.getFkAreaStateIdBirth());

        Set<Long> cityIds = new HashSet<>();
        cityIds.add(clientVo.getFkAreaCityId());
        cityIds.add(clientVo.getFkAreaCityIdEducation());
        cityIds.add(clientVo.getFkAreaCityIdEducation2());
        cityIds.add(clientVo.getFkAreaCityIdBirth());

        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
//            countryNamesByIds = institutionCenterClient.getCountryNamesByIds(countryIds);
            Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                countryNamesByIds = result.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
//            stateNamesByIds = institutionCenterClient.getStateNamesByIds(stateIds);
            Result<Map<Long, String>> stateNamesByIdsResult = institutionCenterClient.getStateNamesByIds(stateIds);
            if (stateNamesByIdsResult.isSuccess() && GeneralTool.isNotEmpty(stateNamesByIdsResult.getData())) {
                stateNamesByIds = stateNamesByIdsResult.getData();
            }
        }
        //根据城市ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
//            cityNamesByIds = institutionCenterClient.getCityNamesByIds(cityIds);
            Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }

        //获取是否有申请方案
        List<ClientOffer> clientOffers = clientOfferMapper.selectList(Wrappers.<ClientOffer>lambdaQuery()
                .eq(ClientOffer::getFkClientId, clientVo.getId()));

        ArrayList<ClientAgentVo> clientAgentVoList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(clientOffers)) {
            //显示代理
            Set<Long> agentIds = clientOffers.stream().map(ClientOffer::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, String> agentNamesMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(agentIds)) {
                agentNamesMap = agentService.getAgentNamesByIds(agentIds);
            }

            Set<String> agentNameSet = Sets.newHashSet();
            for (ClientOffer clientOffer : clientOffers) {
                // 判断是否作废
                if (GeneralTool.isNotEmpty(clientOffer.getStatus()) && clientOffer.getStatus() == 0)
                    continue;
                ClientAgentVo clientAgentVo = new ClientAgentVo();
                clientAgentVo.setAgentContactTel(clientOffer.getAgentContactEmail());
                if (GeneralTool.isNotEmpty(clientOffer.getFkAgentId())) {
                    clientAgentVo.setAgentName(agentNamesMap.get(clientOffer.getFkAgentId()));
                    if (GeneralTool.isNotEmpty(clientAgentVo.getAgentName()) && !agentNameSet.contains(clientAgentVo.getAgentName())) {
                        agentNameSet.add(clientAgentVo.getAgentName());
                        clientAgentVoList.add(clientAgentVo);
                    }
                } else if (GeneralTool.isNotEmpty(clientOffer.getAgentName())) {
                    clientAgentVo.setAgentName(clientOffer.getAgentName());
                    if (!agentNameSet.contains(clientAgentVo.getAgentName())) {
                        clientAgentVoList.add(clientAgentVo);
                    }
                }
            }
            clientVo.setCilentOfferFlag(true);
        } else {
            clientVo.setCilentOfferFlag(false);
        }
        clientVo.setClientAgentDtoList(clientAgentVoList);

        //获取绑定的项目成员
        List<ClientProjectRoleStaffVo> clientProjectRoleStaffVos = studentProjectRoleStaffMapper.selectProjectStaffByClientId(id);
        Set<Long> staffIds = clientProjectRoleStaffVos.stream().map(ClientProjectRoleStaffVo::getFkStaffId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> staffNamesByIds = permissionCenterClient.getStaffNamesByIds(staffIds);
        for (ClientProjectRoleStaffVo clientProjectRoleStaffVo : clientProjectRoleStaffVos) {
            clientProjectRoleStaffVo.setStaffName(staffNamesByIds.get(clientProjectRoleStaffVo.getFkStaffId()));
        }
        clientVo.setClientProjectRoleStaffDtos(clientProjectRoleStaffVos);

        //获取绑定的代理，需求变动
//        List<StudentAgentVo> studentAgentDtoList = studentAgentMapper.getAgentStaffNameByStudentId(id);
//        List<ClientAgentVo> clientAgentVoList = clientAgentMapper.getAgentStaffNameByClientId(id);
//        Set<Long> collect = clientAgentVoList.stream().map(ClientAgentVo::getStaffId).collect(Collectors.toSet());
//        if (GeneralTool.isNotEmpty(collect)) {
//            Map<Long, String> staffNameMap = new HashMap<>();
//            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(collect);
//            if (result.isSuccess() && result.getData() != null) {
//                staffNameMap = result.getData();
//            }
//            for (ClientAgentVo clientAgentDto : clientAgentVoList) {
//                //邮箱
//                Set<String> emails = new HashSet<>();
//                clientAgentDto.setEmails(emails);
//                clientAgentDto.setBdName(staffNameMap.get(clientAgentDto.getStaffId()));
//
//            }
//        }

        if (GeneralTool.isNotEmpty(clientVo.getEducationProject())) {
            //项目说明名称
            switch (clientVo.getEducationProject()) {
                case 0:
                    clientVo.setEducationProjectName(ProjectExtraEnum.THREE_ONE.value);
                    break;
                case 1:
                    clientVo.setEducationProjectName(ProjectExtraEnum.TWO_TWO.value);
                    break;
                case 2:
                    clientVo.setEducationProjectName(ProjectExtraEnum.FOUR_ZERO.value);
                    break;
                case 3:
                    clientVo.setEducationProjectName(ProjectExtraEnum.EXCHANGE_STUDENTS.value);
                    break;
                default:
                    break;
            }
        }
        if (GeneralTool.isNotEmpty(clientVo.getEducationDegree())) {
            //学位情况名称
            switch (clientVo.getEducationDegree()) {
                case 0:
                    clientVo.setEducationDegreeName(ProjectExtraEnum.DOUBLE_DEGREE.value);
                    break;
                case 1:
                    clientVo.setEducationDegreeName(ProjectExtraEnum.INTERNATIONAL_DEGREE.value);
                    break;
                case 2:
                    clientVo.setEducationDegreeName(ProjectExtraEnum.DOMESTIC_DEGREE.value);
                    break;
                default:
                    break;
            }
        }


//        Set<Long> studentIds = new HashSet<>();
//        studentIds.add(clientVo.getId());
//
//        //根据学生ids查询代理名称
//        Map<Long, String> agentNameByStudentIds = new HashMap<>();
//        //根据学生ids获取bd
//        Map<Long, String> bdCodeByStudentIds = new HashMap<>();
//        if (GeneralTool.isNotEmpty(studentIds)) {
//            agentNameByStudentIds = studentAgentService.getAgentNameByStudentIds(studentIds);
//            bdCodeByStudentIds = studentAgentService.getBdCodeByStudentIds(studentIds);
//        }

        Set<Long> clientIds = new HashSet<>();
        clientIds.add(clientVo.getId());
        //根据客户ids查询代理名称
        Map<Long, String> agentNameByClientIds = new HashMap<>();
        //根据客户ids获取bd
        Map<Long, String> bdCodeByClientIds = new HashMap<>();
//        if (GeneralTool.isNotEmpty(clientIds)) {
//            agentNameByClientIds = clientAgentService.getAgentNameByClientIds(clientIds);
//            bdCodeByClientIds = clientAgentService.getBdCodeByClientIds(clientIds);
//        }


//        Result<String> institutionNameResult = institutionCenterClient.getInstitutionName(client.getFkInstitutionIdEducation());
//        if (institutionNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionNameResult.getData())) {
//            clientVo.setEducationInstitutionName(institutionNameResult.getData());
//        }
//        //国际
//        String educationInstitutionName2 = "";
//        Result<String> institutionNamesByIdsResult = institutionCenterClient.getInstitutionName(client.getFkInstitutionIdEducation2());
//        if (institutionNamesByIdsResult.isSuccess() && GeneralTool.isNotEmpty(institutionNamesByIdsResult.getData())) {
//            educationInstitutionName2 = institutionNamesByIdsResult.getData();
//        }
//        clientVo.setEducationInstitutionName2(educationInstitutionName2);
//        List<RStudentToClientApproval> rStudentToClientApprovals = rStudentToClientApprovalMapper.selectList(Wrappers.lambdaQuery(RStudentToClientApproval.class)
//                .eq(RStudentToClientApproval::getFkClientId, id)
//                .isNotNull(RStudentToClientApproval::getFkStudentId)
//                .and(wrapper->wrapper.eq(RStudentToClientApproval::getApprovalStatus,1)
//                        .or().isNull(RStudentToClientApproval::getApprovalStatus))
//        );
        List<Student> students = studentMapper.selectList(Wrappers.lambdaQuery(Student.class).eq(Student::getFkClientId, id));
        clientVo.setIsBusinessCreateStudent(GeneralTool.isNotEmpty(students));
        if (GeneralTool.isNotEmpty(students)) {
            clientVo.setFkStudentId(students.get(0).getId());
            clientVo.setBusinessCreateStudentNum(students.get(0).getNum());
        }
        setName(companyMap, clientVo, countryNamesByIds, stateNamesByIds, cityNamesByIds, agentNameByClientIds, bdCodeByClientIds);
        return clientVo;
    }

    @Override
    public void updateExpectSigningTime(ClientExpectSigningDto clientExpectSigningDto) {
        if (GeneralTool.isEmpty(clientExpectSigningDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Client client = new Client();
        BeanCopyUtils.copyProperties(clientExpectSigningDto, client);
        clientMapper.updateById(client);
    }

    @Override
    public void updateStarLevel(ClientStarLevelDto clientStarLevelDto) {
        if (GeneralTool.isEmpty(clientStarLevelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Client client = new Client();
        BeanCopyUtils.copyProperties(clientStarLevelDto, client);
        clientMapper.updateById(client);
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateClient(id);
        int delete = clientMapper.deleteById(id);
        if (delete <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_CLIENT.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_CLIENT.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_CLIENT.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<ClientApprovalVo> getClientApprovalList(ClientApprovalDto clientApprovalDto, Page page) {
        Boolean isStaffIdApproval = false;
        Long staffIdApproval = 0L;
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        staffIdApproval = Long.valueOf(configValue1);
        if (staffIdApproval.equals(SecureUtil.getStaffId())) {
            isStaffIdApproval = true;
        }
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        //下属包括自己
        List<Long> staffFollowerIds = verifyStudentOfferItemUtils.getStaffFollowerIds(staffInfo.getStaffId());
        staffFollowerIds.add(staffIdApproval);
        Map<Long, String> loginIdMap = permissionCenterClient.getStaffLoginIdByIds(Sets.newHashSet(staffFollowerIds)).getData();
        List<String> loginIds = Lists.newArrayList(loginIdMap.values());

        IPage<ClientApprovalVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ClientApprovalVo> clientApprovalVos = rStudentToClientApprovalMapper.getClientApprovalList(iPage, clientApprovalDto, loginIds, isStaffIdApproval, staffFollowerIds);
        page.setAll((int) iPage.getTotal());
        Set<Long> staffIds = clientApprovalVos.stream().map(ClientApprovalVo::getFkStaffIdApproval).collect(Collectors.toSet());
        Set<Long> staffIdApplys = clientApprovalVos.stream().map(ClientApprovalVo::getFkStaffIdApply).collect(Collectors.toSet());
        staffIdApplys.addAll(staffIds);
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIdApplys).getData();

        Set<Long> fkCompanyIds = clientApprovalVos.stream().map(ClientApprovalVo::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyIdsMap = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds).getData();
        for (ClientApprovalVo clientApprovalVo : clientApprovalVos) {
            if (GeneralTool.isNotEmpty(staffNameMap)) {
                clientApprovalVo.setApprovalName(staffNameMap.get(clientApprovalVo.getFkStaffIdApproval()));
                clientApprovalVo.setApplyName(staffNameMap.get(clientApprovalVo.getFkStaffIdApply()));
            }
            if (GeneralTool.isNotEmpty(companyIdsMap)) {
                clientApprovalVo.setFkCompanyName(companyIdsMap.get(clientApprovalVo.getFkCompanyId()));
            }
        }
        return clientApprovalVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> upateApprovalStatus(ApprovalStatusDto approvalStatusDto) {
        List<String> list = new ArrayList<>();
        if (approvalStatusDto.getApprovalStatus() == 0) {
            //获取审批人
//            Map<Long, Integer> companySettlementConfigInfoMap = permissionCenterClient.getCompanySettlementConfigInfoMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key);
//            Integer approverIdInteger = companySettlementConfigInfoMap.get(SecureUtil.getFkCompanyId());
//            Long approverId = Long.valueOf(approverIdInteger);
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
            String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
            Long approverId = Long.valueOf(configValue1);

            List<Long> fkStudentIdList = approvalStatusDto.getFkStudentIdList();
            LambdaQueryWrapper<RStudentToClientApproval> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(RStudentToClientApproval::getFkStudentId, approvalStatusDto.getFkStudentIdList()).ne(RStudentToClientApproval::getApprovalStatus, 2);
            List<RStudentToClientApproval> rStudentToClientApprovals = rStudentToClientApprovalMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(rStudentToClientApprovals)) {
                Set<Long> ids = rStudentToClientApprovals.stream().map(RStudentToClientApproval::getFkStudentId).collect(Collectors.toSet());
                fkStudentIdList.removeAll(ids);
                List<Student> students = studentMapper.selectBatchIds(ids);
                for (Student student : students) {
                    list.add(student.getNum() + " " + student.getName());
                }
            }
            Map<Long, List<ClientSourceStudentVo>> studentsMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(fkStudentIdList)) {
                studentsMap = getStudentsMap(fkStudentIdList, approvalStatusDto.getCountryId());
            }

            //获取权限员工id

            for (Long fkStudentId : fkStudentIdList) {
                Student student = studentMapper.selectById(fkStudentId);
                if (GeneralTool.isEmpty(student)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("student_info_null"));
                }
                List<ClientSourceStudentVo> clientSourceStudentVos = studentsMap.get(fkStudentId);
                Client client = BeanCopyUtils.objClone(student, Client::new);
                client.setId(null);

                client.setFkCompanyId(SecureUtil.getFkCompanyId());
                client.setIsActive(true);
                utilService.setCreateInfo(client);
                int clientId = clientMapper.insert(client);
                ClientStaffDto clientStaffDto = new ClientStaffDto();
                clientStaffDto.setFkClientId(client.getId());
                clientStaffDto.setFkStaffId(SecureUtil.getStaffId());
                clientStaffService.addClientStaff(clientStaffDto);

                client.setNum(GetStringUtils.getClientNum(client.getId()));
                clientMapper.updateById(client);

                //保存学生信息
//                Student studentnew = new Student();
//                studentnew.setFkClientId(client.getId());
//                LambdaQueryWrapper<Student> studentLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                studentLambdaQueryWrapper.eq(Student::getId,fkStudentId);
//                studentMapper.update(studentnew,studentLambdaQueryWrapper);

                RStudentToClientApproval rStudentToClientApproval = BeanCopyUtils.objClone(approvalStatusDto, RStudentToClientApproval::new);
                rStudentToClientApproval.setFkStudentId(fkStudentId);
                rStudentToClientApproval.setFkCompanyId(SecureUtil.getFkCompanyId());
                rStudentToClientApproval.setFkStaffIdApply(SecureUtil.getStaffId());
                rStudentToClientApproval.setApprovalStatus(1);
                rStudentToClientApproval.setGmtModified(new Date());
                rStudentToClientApproval.setApprovalOpinion("同意");
                rStudentToClientApproval.setFkClientId(client.getId());
                if (approverId > 0) {
                    rStudentToClientApproval.setFkStaffIdApproval(approverId);
                }
                utilService.setCreateInfo(rStudentToClientApproval);
                rStudentToClientApprovalMapper.insert(rStudentToClientApproval);

                if (GeneralTool.isNotEmpty(clientSourceStudentVos)) {
                    Map<Long, List<AgentStaff>> agnetStaffListMap = Maps.newHashMap();
                    Set<Long> agentIds = clientSourceStudentVos.stream().map(ClientSourceStudentVo::getFkAgentId).collect(Collectors.toSet());
                    if (GeneralTool.isNotEmpty(agentIds)) {
                        List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                                .in(AgentStaff::getFkAgentId, agentIds)
                                .eq(AgentStaff::getIsActive, true));
                        if (GeneralTool.isNotEmpty(agentStaffs)) {
                            agnetStaffListMap = agentStaffs.stream().collect(Collectors.groupingBy(AgentStaff::getFkAgentId));
                        }
                    }

                    List<ClientSource> clientSources = BeanCopyUtils.copyListProperties(clientSourceStudentVos, ClientSource::new);
                    for (ClientSource clientSource : clientSources) {
                        clientSource.setFkTableId(fkStudentId);
                        List<AgentStaff> agentStaffs = agnetStaffListMap.get(clientSource.getFkAgentId());
                        if (GeneralTool.isNotEmpty(agentStaffs)) {
                            clientSource.setFkStaffId(agentStaffs.get(0).getFkStaffId());
                        }
                        clientSource.setFkClientId(client.getId());
                        utilService.setCreateInfo(clientSource);
                        clientSourceMapper.insert(clientSource);
                        // 增加共享代理
                        List<AgentCompany> agentCompanyList = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery()
                                .eq(AgentCompany::getFkAgentId, clientSource.getFkAgentId())
                                .eq(AgentCompany::getFkCompanyId, SecureUtil.getFkCompanyId()));
                        if (GeneralTool.isEmpty(agentCompanyList)) {
                            AgentCompany agentCompany = new AgentCompany();
                            agentCompany.setFkAgentId(clientSource.getFkAgentId());
                            agentCompany.setFkCompanyId(SecureUtil.getFkCompanyId());
                            utilService.setCreateInfo(agentCompany);
                            agentCompanyMapper.insert(agentCompany);
                        }
                    }
                }

                //不卡流程，直接批量审批通过，同时发邮件给审批人
                sentEmailToApproval(student, rStudentToClientApproval, approverId);
                //TODO 绑定学生暂时不需要发邮件 lucky
//                ClientDto clientDto = new ClientDto();
//                clientDto.setId(client.getId());
//                Long staffId = SecureUtil.getStaffId();
//                Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(staffId);
//                List<Long> followerIds = result.getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
//                followerIds.add(staffId);
//                //根据资源id获取新增的学生
//                List<ClientListInfoVo> clientDtos  = clientMapper.getResourceList(null, clientDto, followerIds, false, true);
//                ClientListInfoVo clientListInfoVo = clientDtos.get(0);
//                List<ClientListInfoDto> clientListInfoDtos = new ArrayList<>(clientDtos.size());
//                try {
//                    pack(clientDtos, clientDto, clientListInfoDtos);
//                    sendEmailToBDAndAssistant(clientListInfoVo,clientListInfoDtos);
//                }catch (Exception e){
//                    log.error("发送邮件最终失败, clientListInfoVo: {}, clientListInfoDtos: {}",
//                            clientListInfoVo, clientListInfoDtos, e);
//                }
            }
        } else if (approvalStatusDto.getApprovalStatus() == 1) {

            RStudentToClientApproval rStudentToClientApproval = rStudentToClientApprovalMapper.selectById(approvalStatusDto.getId());
            rStudentToClientApproval.setApprovalStatus(approvalStatusDto.getApprovalStatus());
            rStudentToClientApproval.setApprovalOpinion(approvalStatusDto.getApprovalOpinion());
            rStudentToClientApproval.setFkStaffIdApproval(SecureUtil.getStaffId());
            Student student = studentMapper.selectById(rStudentToClientApproval.getFkStudentId());
            if (GeneralTool.isEmpty(student)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_info_null"));
            }
//            Map<Long, List<ClientSourceStudentVo>> studentsMap = getStudentsMap(Lists.newArrayList(rStudentToClientApproval.getFkStudentId()));
            Client client = BeanCopyUtils.objClone(student, Client::new);
            client.setId(null);
            utilService.setCreateInfo(client);
            client.setIsActive(true);
            int clientId = clientMapper.insert(client);
            client.setNum(GetStringUtils.getClientNum(client.getId()));
            clientMapper.updateById(client);

            //保存学生信息
//            Student studentnew = new Student();
//            studentnew.setFkClientId(client.getId());
//            LambdaQueryWrapper<Student> studentLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            studentLambdaQueryWrapper.eq(Student::getId,approvalStatusDto.getFkStudentId());
//            studentMapper.update(studentnew,studentLambdaQueryWrapper);

            rStudentToClientApproval.setFkClientId(client.getId());
            rStudentToClientApproval.setGmtModified(null);
            rStudentToClientApproval.setGmtModifiedUser(null);
            utilService.setUpdateInfo(rStudentToClientApproval);
            rStudentToClientApprovalMapper.updateById(rStudentToClientApproval);

//            List<ClientSourceStudentVo> clientSourceStudentDtos = studentsMap.get(rStudentToClientApproval.getFkStudentId());
//            if (GeneralTool.isNotEmpty(clientSourceStudentDtos)){
//                List<ClientSource> clientSources = BeanCopyUtils.copyListProperties(clientSourceStudentDtos, ClientSource::new);
//                for (ClientSource clientSource : clientSources) {
//                    clientSource.setFkClientId(client.getId());
//                    utilService.setCreateInfo(clientSource);
//                    clientSourceMapper.insert(clientSource);
//                }
//            }

        } else if (approvalStatusDto.getApprovalStatus() == 2) {
            RStudentToClientApproval rStudentToClientApproval = rStudentToClientApprovalMapper.selectById(approvalStatusDto.getId());
            rStudentToClientApproval.setApprovalStatus(approvalStatusDto.getApprovalStatus());
            rStudentToClientApproval.setApprovalOpinion(approvalStatusDto.getApprovalOpinion());
            rStudentToClientApproval.setFkStaffIdApproval(SecureUtil.getStaffId());
            utilService.setUpdateInfo(rStudentToClientApproval);
            rStudentToClientApprovalMapper.updateById(rStudentToClientApproval);
        } else if (approvalStatusDto.getApprovalStatus() == -1) {
            rStudentToClientApprovalMapper.deleteById(approvalStatusDto.getId());
        }
        return list;
    }


    /**
     * 获取符合条件的学生资源
     *
     * @param studentIds
     * @param countryId  申请国家id
     * @return
     */
    private Map<Long, List<ClientSourceStudentVo>> getStudentsMap(List<Long> studentIds, Long countryId) {
        Map<Long, List<ClientSourceStudentVo>> result = Maps.newHashMap();
        List<Student> students = studentMapper.selectBatchIds(studentIds);
        if (GeneralTool.isEmpty(students)) {
            return Collections.emptyMap();
        }
        Map<Long, Student> studentMap = students.stream().collect(Collectors.toMap(Student::getId, Function.identity()));


        // 增加国家的过滤
        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(Wrappers.lambdaQuery(StudentOfferItem.class)
                .in(StudentOfferItem::getFkStudentId, studentIds)
                .eq(StudentOfferItem::getStatus, 1)
                .eq(StudentOfferItem::getFkAreaCountryId, countryId)
        );
        if (GeneralTool.isNotEmpty(studentOfferItems)) {
            Map<Long, Long> maxOfferItemMap = new HashMap<>();
            // 增加国家的过滤
            List<BaseSelectEntity> maxOfferItemByStudentIds = studentOfferItemMapper.getMaxOfferItemByStudentIds(studentIds, countryId);
            if (GeneralTool.isNotEmpty(maxOfferItemByStudentIds)) {
                maxOfferItemMap = maxOfferItemByStudentIds.stream().collect(Collectors.toMap(BaseSelectEntity::getId, BaseSelectEntity::getDeputyId, (k1, k2) -> k1));
            }
            List<Long> stepIds = Lists.newArrayList(6L, 7L, 8L, 10L);
            Map<Long, List<StudentOfferItem>> studentOfferItemsMap = studentOfferItems.stream().collect(Collectors.groupingBy(StudentOfferItem::getFkStudentId));

            if (GeneralTool.isNotEmpty(studentOfferItemsMap)) {

                for (Map.Entry<Long, List<StudentOfferItem>> entry : studentOfferItemsMap.entrySet()) {
                    Long studentId = entry.getKey();
                    Student student = studentMap.get(studentId);
                    String studentNum = student.getNum();

                    List<ClientSourceStudentVo> clientSourceStudentVos = Lists.newArrayList();
                    List<StudentOfferItem> offerItems = entry.getValue();
                    // 若一个国家真是绑定了2个代理，任选其中一个进行推荐绑定，不需要插入多条记录。
                    StudentOfferItem studentOfferItem = offerItems.get(0);
                    if (GeneralTool.isNotEmpty(studentOfferItem)) {
                        ClientSourceStudentVo clientSourceStudentVo = new ClientSourceStudentVo();
                        clientSourceStudentVo.setFkAgentId(studentOfferItem.getFkAgentId());
                        if (GeneralTool.isNotEmpty(maxOfferItemMap)
                                && GeneralTool.isNotEmpty(maxOfferItemMap.get(studentId))
                                && stepIds.contains(maxOfferItemMap.get(studentId))) {
                            clientSourceStudentVo.setFkTableName(ProjectKeyEnum.CLIENT_SOURCE_TYPE_BMS.key);
                        } else {
                            clientSourceStudentVo.setFkTableName(ProjectKeyEnum.CLIENT_SOURCE_TYPE_BMS_NOT_OS.key);
                        }

                        clientSourceStudentVo.setFkTableValue(studentNum);
                        clientSourceStudentVos.add(clientSourceStudentVo);
                    }
                    result.put(studentId, clientSourceStudentVos);
                }

            }
        } else {
            //服务费
            List<StudentServiceFee> studentServiceFeeList = studentServiceFeeMapper.getClientStudentServiceFee(studentIds, countryId);
            Map<Long, List<StudentServiceFee>> studentServiceFeeMap = studentServiceFeeList.stream().collect(Collectors.groupingBy(StudentServiceFee::getFkStudentId));

            for (Map.Entry<Long, List<StudentServiceFee>> entry : studentServiceFeeMap.entrySet()) {
                Long studentId = entry.getKey();
                Student student = studentMap.get(studentId);
                String studentNum = student.getNum();

                List<ClientSourceStudentVo> clientSourceStudentVos = Lists.newArrayList();
                List<StudentServiceFee> serviceFeeList =  entry.getValue();
                // 若一个国家真是绑定了2个代理，任选其中一个进行推荐绑定，不需要插入多条记录。
                StudentServiceFee studentServiceFee =   serviceFeeList.get(0);
                if (GeneralTool.isNotEmpty(studentServiceFee)) {
                    ClientSourceStudentVo clientSourceStudentVo = new ClientSourceStudentVo();
                    clientSourceStudentVo.setFkAgentId(studentServiceFee.getFkAgentId());
                    clientSourceStudentVo.setFkTableName(ProjectKeyEnum.CLIENT_SOURCE_TYPE_AGENT.key);
                    clientSourceStudentVo.setFkTableValue(studentNum);
                    clientSourceStudentVos.add(clientSourceStudentVo);
                }
                result.put(studentId, clientSourceStudentVos);
            }




        }
        return result;
    }

    public Long bindingAndUpateApprovalStatus(ApprovalStatusDto approvalStatusDto) {
        if (approvalStatusDto.getApprovalStatus() == 0) {
            //获取审批人
//            Map<Long, Integer> companySettlementConfigInfoMap = permissionCenterClient.getCompanySettlementConfigInfoMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key);
//            Integer approverIdInteger = companySettlementConfigInfoMap.get(SecureUtil.getFkCompanyId());
//            Long approverId = Long.valueOf(approverIdInteger);
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
            String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
            Long approverId = Long.valueOf(configValue1);

            Long fkStudentId = approvalStatusDto.getFkStudentId();
            if (GeneralTool.isEmpty(fkStudentId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
            }
            Student student = studentMapper.selectById(fkStudentId);
            if (GeneralTool.isEmpty(student)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_info_null"));
            }
            Map<Long, List<ClientSourceStudentVo>> studentsMap = getStudentsMap(Lists.newArrayList(fkStudentId), approvalStatusDto.getCountryId());
            Client client = BeanCopyUtils.objClone(student, Client::new);
            client.setId(null);
            client.setFkCompanyId(SecureUtil.getFkCompanyId());
            utilService.setCreateInfo(client);
            int clientId = clientMapper.insert(client);
            client.setNum(GetStringUtils.getClientNum(client.getId()));
            clientMapper.updateById(client);

            RStudentToClientApproval rStudentToClientApproval = BeanCopyUtils.objClone(approvalStatusDto, RStudentToClientApproval::new);
            rStudentToClientApproval.setFkStudentId(fkStudentId);
            rStudentToClientApproval.setFkCompanyId(SecureUtil.getFkCompanyId());
            rStudentToClientApproval.setFkStaffIdApply(SecureUtil.getStaffId());
            rStudentToClientApproval.setApprovalStatus(1);
            rStudentToClientApproval.setGmtModified(new Date());
            rStudentToClientApproval.setApprovalOpinion("同意");
            rStudentToClientApproval.setFkClientId(client.getId());
            if (approverId > 0) {
                rStudentToClientApproval.setFkStaffIdApproval(approverId);
            }
            utilService.setCreateInfo(rStudentToClientApproval);
            rStudentToClientApprovalMapper.insert(rStudentToClientApproval);

            List<ClientSourceStudentVo> clientSourceStudentVos = studentsMap.get(rStudentToClientApproval.getFkStudentId());
            if (GeneralTool.isNotEmpty(clientSourceStudentVos)) {
                Map<Long, List<AgentStaff>> agnetStaffListMap = Maps.newHashMap();
                Set<Long> agentIds = clientSourceStudentVos.stream().map(ClientSourceStudentVo::getFkAgentId).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(agentIds)) {
                    List<AgentStaff> agentStaffs = agentStaffMapper.selectList(Wrappers.lambdaQuery(AgentStaff.class)
                            .in(AgentStaff::getFkAgentId, agentIds)
                            .eq(AgentStaff::getIsActive, true));
                    if (GeneralTool.isNotEmpty(agentStaffs)) {
                        agnetStaffListMap = agentStaffs.stream().collect(Collectors.groupingBy(AgentStaff::getFkAgentId));
                    }
                }

                List<ClientSource> clientSources = BeanCopyUtils.copyListProperties(clientSourceStudentVos, ClientSource::new);
                for (ClientSource clientSource : clientSources) {
                    List<AgentStaff> agentStaffs = agnetStaffListMap.get(clientSource.getFkAgentId());
                    clientSource.setFkClientId(client.getId());
                    clientSource.setFkTableId(fkStudentId);
                    if (GeneralTool.isNotEmpty(agentStaffs)) {
                        clientSource.setFkStaffId(agentStaffs.get(0).getFkStaffId());
                    }
                    utilService.setCreateInfo(clientSource);
                    clientSourceMapper.insert(clientSource);
                    // 增加共享代理
                    List<AgentCompany> agentCompanyList = agentCompanyMapper.selectList(Wrappers.<AgentCompany>lambdaQuery()
                            .eq(AgentCompany::getFkAgentId, clientSource.getFkAgentId())
                            .eq(AgentCompany::getFkCompanyId, SecureUtil.getFkCompanyId()));
                    if (GeneralTool.isEmpty(agentCompanyList)) {
                        AgentCompany agentCompany = new AgentCompany();
                        agentCompany.setFkAgentId(clientSource.getFkAgentId());
                        agentCompany.setFkCompanyId(SecureUtil.getFkCompanyId());
                        utilService.setCreateInfo(agentCompany);
                        agentCompanyMapper.insert(agentCompany);
                    }
                }
            }

            //不卡流程，直接批量审批通过，同时发邮件给审批人
            sentEmailToApproval(student, rStudentToClientApproval, approverId);
            return client.getId();
        }
        return null;
    }

    /**
     * @param sameStudentClientDto
     * @return
     */
    @Override
    public List<SameStudentClientVo> getSameStudentClients(SameStudentClientDto sameStudentClientDto) {

        //申请澳洲
        sameStudentClientDto.setFkAreaCountryId(6L);
        //当前步骤为收到签证函（CAS/COE/I20）、递交签证完成（Visa Submitted）、获得签证（Visa Granted）、入学登记完成（Enrolled）的
        sameStudentClientDto.setStepIds(Lists.newArrayList(6L, 7L, 10L, 8L));
        List<SameStudentClientVo> sameStudentClientVos = clientMapper.getSameStudentClients(sameStudentClientDto);
        if (GeneralTool.isEmpty(sameStudentClientVos)) {
            return Collections.emptyList();
        }

        Set<Long> studentIds = sameStudentClientVos.stream().map(SameStudentClientVo::getId).collect(Collectors.toSet());
        List<RStudentToClientApproval> rStudentToClientApprovals = rStudentToClientApprovalMapper.selectList(Wrappers.lambdaQuery(RStudentToClientApproval.class)
                .in(RStudentToClientApproval::getFkStudentId, studentIds));

        if (GeneralTool.isNotEmpty(rStudentToClientApprovals)) {
            Set<Long> bindingStudentIds = rStudentToClientApprovals.stream().map(RStudentToClientApproval::getFkStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
            //过滤已经绑定的学生
            sameStudentClientVos = sameStudentClientVos.stream().filter(stu -> !bindingStudentIds.contains(stu.getId())).collect(Collectors.toList());
        }
        if (GeneralTool.isEmpty(sameStudentClientVos)) {
            return Collections.emptyList();
        }

        Set<Long> agentIds = sameStudentClientVos.stream().map(SameStudentClientVo::getFkAgentId).collect(Collectors.toSet());
        Map<Long, String> agentNamesByIds = agentService.getAgentNamesByIds(agentIds);
        for (SameStudentClientVo sameStudentClientVo : sameStudentClientVos) {
            sameStudentClientVo.setAgentName(agentNamesByIds.get(sameStudentClientVo.getFkAgentId()));
        }

        return sameStudentClientVos;
    }

    /**
     * 申请绑定
     *
     * @param bindingStudentClientDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ClientVo bindingStudentClient(BindingStudentClientDto bindingStudentClientDto) {
        ApprovalStatusDto approvalStatusDto = new ApprovalStatusDto();
        approvalStatusDto.setApprovalStatus(0);
        approvalStatusDto.setFkStudentId(bindingStudentClientDto.getId());
        approvalStatusDto.setCountryId(bindingStudentClientDto.getCountryId());
        Long clientId = bindingAndUpateApprovalStatus(approvalStatusDto);
        if (GeneralTool.isEmpty(clientId)) {
            return null;
        }
        return findClientById(clientId);
    }

    @Override
    public List<FocExportVo> getClientOptions() {
//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key).getData();
        Long staffIdApproval = 0L;
//        if (GeneralTool.isNotEmpty(configDto)){
//            String configJson = configDto.getValue1();
//            JSONObject configJsonObject = JSON.parseObject(configJson);
//            if (SecureUtil.getFkCompanyId().equals(3L)){
//                String iae = configJsonObject.getString("IAE");
//                if (GeneralTool.isNotEmpty(iae)){
//                    staffIdApproval = Long.valueOf(iae);
//                }
//            }else {
//                String gea = configJsonObject.getString("OTHER");
//                if (GeneralTool.isNotEmpty(gea)){
//                    staffIdApproval = Long.valueOf(gea);
//                }
//            }
//        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.STUDENT_TO_CLIENT_APPROVER_DEFAULT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        staffIdApproval = Long.valueOf(configValue1);


        boolean flag = staffIdApproval > 0;
        Field[] declaredFields = ClientListExportDto.class.getDeclaredFields();
        Field[] fields = Arrays.stream(declaredFields).filter(d -> !"studentSource".equals(d.getName())
                && !"sourceAgentName".equals(d.getName())
                && !"sourceBdName".equals(d.getName())
                && !"isEnterCountryName".equals(d.getName())).toArray(Field[]::new);
        CommonUtil<ClientListExportDto> commonUtil = new CommonUtil<>();
        StaffConfig config = permissionCenterClient.getStaffConfigByType(TableEnum.SALE_CLIENT.key);
        if (config == null || StringUtils.isBlank(config.getConfigValue())) {
            if (flag) {
                return commonUtil.getOptions(ClientListExportDto.class);
            } else {
                return commonUtil.getOptions(fields);
            }
        }
        if (flag) {
            return commonUtil.getOptions(ClientListExportDto.class, config.getConfigValue());
        } else {
            return commonUtil.getOptions(fields, config.getConfigValue());
        }
    }

    /**
     * 创建业务学生
     *
     * @param createBusinessStudentDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createBusinessStudent(CreateBusinessStudentDto createBusinessStudentDto) {
        //复制学生
        Student student = BeanCopyUtils.objClone(createBusinessStudentDto, Student::new);
        student.setId(null);
        student.setGmtModifiedUser(null);
        student.setGmtModified(null);
        student.setFkClientId(createBusinessStudentDto.getId());
        utilService.setCreateInfo(student);
        studentMapper.insert(student);
        student.setNum(GetStringUtils.getStudentNum(student.getId()));
        studentMapper.updateById(student);
        //加关系表
//        RStudentToClientApproval studentToClientApproval = new RStudentToClientApproval();
//        studentToClientApproval.setFkClientId(createBusinessStudentDto.getId());
//        studentToClientApproval.setFkCompanyId(createBusinessStudentDto.getFkCompanyId());
//        studentToClientApproval.setFkStudentId(student.getId());
//        utilService.setCreateInfo(studentToClientApproval);
//        rStudentToClientApprovalMapper.insert(studentToClientApproval);

        // 创建业务学生，根据推荐所绑定的代理，进行自动绑定代理。
        // s_client_source是否存在记录
        ClientSource clientSource = clientSourceMapper.selectOne(Wrappers.<ClientSource>lambdaQuery()
                .eq(ClientSource::getFkClientId, createBusinessStudentDto.getId())
                .last("limit 1 "));
        if (GeneralTool.isNotEmpty(clientSource) && GeneralTool.isNotEmpty(clientSource.getFkAgentId())) {
            StudentAgent studentAgent = new StudentAgent();
            studentAgent.setFkStudentId(student.getId());
            studentAgent.setFkAgentId(clientSource.getFkAgentId());
            studentAgent.setIsActive(true);
            studentAgent.setActiveDate(new Date());
            utilService.setCreateInfo(studentAgent);
            studentAgentMapper.insert(studentAgent);
        }
        return student.getId();
    }

    /**
     * 绑定业务学生
     *
     * @param boundBusinessStudentDto 参数
     * @return
     */
    @Override
    public void boundBusinessStudent(BoundBusinessStudentDto boundBusinessStudentDto) {
        List<Student> students = studentMapper.selectBatchIds(boundBusinessStudentDto.getFkStudentIds());
        if (GeneralTool.isNotEmpty(students)) {
            for (Student student : students) {
                student.setFkClientId(boundBusinessStudentDto.getFkClientId());
                utilService.setUpdateInfo(student);
            }
            studentService.updateBatchById(students);
        }
    }

    /**
     * 取消绑定业务学生
     *
     * @param
     * @return
     */
    @Override
    public void unbindBusinessStudent(Set<Long>studentIds) {
        if(GeneralTool.isNotEmpty(studentIds)){
            List<Student> students = studentMapper.selectList(Wrappers.<Student>lambdaQuery().in(Student::getId, studentIds));
            if (GeneralTool.isNotEmpty(students)) {
                Set<Long> ids = students.stream().map(Student::getId).collect(Collectors.toSet());
                Student student = new Student();
                utilService.setUpdateInfo(student);
                studentMapper.update(student, Wrappers.<Student>lambdaUpdate().in(Student::getId, ids).set(Student::getFkClientId, null));
            }
        }

    }

    /**
     * 发送邮箱给审批人
     */
    private void sentEmailToApproval(Student student, RStudentToClientApproval rStudentToClientApproval, Long approverId) {
        //获取审批人
        if (approverId > 0) {
            String staffIdApplyName = permissionCenterClient.getStaffName(SecureUtil.getStaffId()).getData();


            ApprovalItemVo approvalItemVo = this.clientMapper.selectClientStudentItem(student.getId());

            List<RemindTaskDto> remindTaskDtos = Lists.newArrayList();
            //TODO CLIENT_APPROVAL_NOTICE
            //String taskTitle = "学生：" + student.getName() + "，国家：澳洲，申请绑定学生通知";
            String taskTitle = null;
            String taskRemark = "<div class=\"desc\">\n" +
                    "    <div>申请绑定学生通知</div>\n" +
                    "    <div>学生：" + student.getName() + "，国家：澳洲" + "</div>\n" +
                    "    <div>代理：" + approvalItemVo.getAgentName() + "</div>\n" +
                    "    <div>最高状态：" + approvalItemVo.getMaxStepName() + "</div>\n" +
                    "    <div>申请人：" + staffIdApplyName + "申请日期：" + DateUtil.format(rStudentToClientApproval.getGmtCreate(), "yyyy-MM-dd HH:mm:ss") + "</div>\n" +
                    "</div>";
            Map<String,String> map = Maps.newHashMap();
            //获取中英文配置
            Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
            String versionValue2 = versionConfigMap.get(student.getFkCompanyId());
            if(!versionValue2.equals("en")){
                taskTitle = "【系统提醒】学生："+ student.getName() +"，"+"澳洲"+"申请绑定学生通知";
            }else {
                taskTitle = "【System Reminder】Student："+ student.getName() +"，"+"Australia"+"Apply to Bind Student Notification";
            }

            map.put("studentName",student.getName());
            map.put("agentName",approvalItemVo.getAgentName());
            map.put("maxStepName",approvalItemVo.getMaxStepName());
            map.put("applyName",staffIdApplyName);
            map.put("approvalDate",DateUtil.format(rStudentToClientApproval.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"));
            map.put("versionValue",versionValue2);
            RemindTaskDto remindTaskDto = new RemindTaskDto();
            remindTaskDto.setTaskTitle(taskTitle);
            remindTaskDto.setTaskRemark(taskRemark);
            //邮件方式发送
            remindTaskDto.setRemindMethod("1");
            //默认执行中
            remindTaskDto.setStatus(1);
            //默认背景颜色
            remindTaskDto.setFkRemindEventTypeKey(EmailTemplateEnum.CLIENT_APPROVAL_NOTICE.getEmailTemplateKey());
            remindTaskDto.setTaskBgColor("#3788d8");
            remindTaskDto.setFkStaffId(approverId);
            remindTaskDto.setStartTime(new Date());
            remindTaskDto.setAdvanceDays("0");
            remindTaskDto.setFkTableName(EmailTemplateEnum.CLIENT_APPROVAL_NOTICE.getEmailTemplateKey());
            remindTaskDto.setFkTableId(rStudentToClientApproval.getId());
            remindTaskDto.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
            remindTaskDtos.add(remindTaskDto);
            try {
                log.info("==========================================={}", remindTaskDtos.get(0).getTaskRemark());
                Result<Boolean> booleanResult = reminderCenterClient.batchAddTask(remindTaskDtos);

                List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
                emailSenderQueue.setFkTableId(student.getId());
                emailSenderQueue.setEmailTitle(taskTitle);
                emailSenderQueue.setFkTableName(TableEnum.SALE_STUDENT.key);
                emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.CLIENT_APPROVAL_NOTICE.getEmailTemplateKey());
                emailSenderQueue.setOperationTime(new Date());
                emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
                emailSenderQueue.setEmailTo(approverId.toString());
                emailSenderQueueList.add(emailSenderQueue);
                Result<Boolean> result = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                if (!result.isSuccess()) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage(booleanResult.getMessage()));
                }
                //reminderCenterClient.batchAdd(remindTaskDtos);
            } catch (Exception e) {
                log.error("添加发起通知失败：", e);
            }

        }
    }

    /**
     * 发送邮箱给跟进BD和BD助理
     */
    private void sendEmailToBDAndAssistant( ClientListInfoVo clientListInfoVo, List<ClientListInfoDto> clientListInfoDtos) {

        ClientListInfoDto clientListInfoDto = clientListInfoDtos.get(0);

        //需要发送邮件的人
        List<Long> staffIds = Lists.newArrayList();
        //获取学生资源新增学生提醒
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_CLIENT_ADD.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        //获取BD助理
        com.alibaba.fastjson.JSONArray jsonArray = JSON.parseArray(configValue1);
        staffIds.addAll(jsonArray.toJavaList(Long.class));
        staffIds.add(clientListInfoVo.getSourceStaffId());

        List<RemindTaskDto> remindTaskDtos = Lists.newArrayList();
        if(GeneralTool.isNotEmpty(clientListInfoVo)){
            String taskTitle = "【新增学生资源通知】学生:"+clientListInfoVo.getName();
            Map<String, String> dataModel = new HashMap<>();
            dataModel.put("studentName", clientListInfoVo.getName());
            dataModel.put("recommendType", clientListInfoDto.getFkTableNameValue());
            dataModel.put("source", clientListInfoVo.getStudentSource());
            dataModel.put("agentName", clientListInfoDto.getSourceAgentName());
            dataModel.put("bdName", clientListInfoDto.getSourceBdName());
            dataModel.put("creator", clientListInfoVo.getGmtCreateUser());
            dataModel.put("createTime", DateUtil.format(clientListInfoVo.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"));

// 动态替换 sourceLabel
            if (clientListInfoDto.getFkTableNameValue().equals("AIS NOT OS")||clientListInfoDto.getFkTableNameValue().equals("AIS")) {
                dataModel.put("sourceLabel", "学生编号");
            } else if(clientListInfoDto.getFkTableNameValue().equals("CRM")){
                dataModel.put("sourceLabel", "合同编号");
            }
            String taskRemark = MyStringUtils.getReminderTemplate(dataModel, SaleCenterConstant.STUDENT_RESOURCE_CREATED_NOTICE);
            for(Long staffId:staffIds){
                RemindTaskDto remindTaskDto = new RemindTaskDto();
                remindTaskDto.setTaskTitle(taskTitle);
                remindTaskDto.setTaskRemark(taskRemark);
                //邮件方式发送
                remindTaskDto.setRemindMethod("1");
                //默认执行中
                remindTaskDto.setStatus(1);
                //默认背景颜色
                remindTaskDto.setFkRemindEventTypeKey(ProjectKeyEnum.STUDENT_RESOURCE_CREATED_NOTICE.key);
                remindTaskDto.setTaskBgColor("#3788d8");
                remindTaskDto.setFkStaffId(staffId);
                //remindTaskDto.setFkStaffId(1584L);
                remindTaskDto.setStartTime(new Date());
                remindTaskDto.setAdvanceDays("0");
                remindTaskDto.setFkTableName(TableEnum.SALE_CLIENT.key);
                remindTaskDto.setFkTableId(clientListInfoVo.getId());
//                remindTaskDto.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
                remindTaskDtos.add(remindTaskDto);
            }


            try {
                log.info("==========================================={}", remindTaskDtos.get(0).getTaskRemark());
                reminderCenterClient.batchAdd(remindTaskDtos);
            } catch (Exception e) {
                log.error("添加发起通知失败：", e);
            }
        }
    }





    @Override
    public Map<String, String> getIsExistStudent(String clientName, String email, String mobile, String passpost, String birthday, Long id, Long companyId) {
        Map<String, String> map = new LinkedHashMap<>();
        String type = "";
        List<Client> client = null;
        if (GeneralTool.isNotBlank(birthday)) {
            LambdaQueryWrapper<Client> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Client::getName, clientName)
                    .eq(GeneralTool.isNotEmpty(companyId), Client::getFkCompanyId, companyId)
                    .eq(Client::getBirthday, birthday)
                    .ne(GeneralTool.isNotEmpty(id), Client::getId, id);
            List<Client> isExitClientBirthday = clientMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(isExitClientBirthday)) {
                client = isExitClientBirthday;
                type = "birthday";
            }
        }
        if (GeneralTool.isEmpty(client) && GeneralTool.isNotBlank(passpost)) {
            LambdaQueryWrapper<Client> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(Client::getPassportNum, passpost)
                    .eq(Client::getFkCompanyId, companyId)
                    .ne(GeneralTool.isNotEmpty(id), Client::getId, id);
            List<Client> isExitClientPassport = clientMapper.selectList(lambdaQueryWrapper);
            if (GeneralTool.isNotEmpty(isExitClientPassport)) {
                client = isExitClientPassport;
                type = "passpost";
            }
        }

        int temp = 0;
        if (GeneralTool.isNotEmpty(client)) {
            List<Long> ids = client.stream().map(Client::getId).collect(Collectors.toList());
            List<AgentVo> clientAgents = clientMapper.getClientAgents(ids);

            for (Client clientData : client) {
                if (!clientData.getId().equals(id)) {
                    temp++;
                    StringBuilder sb = new StringBuilder();
                    sb.append("【").append(temp).append("】")
                            .append(LocaleMessageUtils.getMessage("m_client")).append("：").append(clientData.getName()).append("，");
                    switch (type) {
                        case "birthday":
                            Optional.ofNullable(clientData.getBirthday()).ifPresent(d ->
                                    sb.append(LocaleMessageUtils.getMessage("BIRTHDAY")).append("：").append(new SimpleDateFormat("yyyy-MM-dd").format(clientData.getBirthday())).append("，")
                                            .append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(clientData.getNum()).append("，")
                                            .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！"));
                            break;
                        case "passpost":
                            Optional.ofNullable(clientData.getPassportNum()).ifPresent(d ->
                                    sb.append(LocaleMessageUtils.getMessage("PASSPOST")).append("：").append(clientData.getPassportNum()).append("，")
                                            .append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(clientData.getNum()).append("，")
                                            .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！"));
                    }
                    if (GeneralTool.isNotEmpty(clientAgents)) {
                        sb.append(LocaleMessageUtils.getMessage("STUDENT_AGENT_IS")).append("：<br/>");
                        Set<Long> staffIds = clientAgents.stream().map(AgentVo::getFkStaffId).collect(Collectors.toSet());
                        List<StaffVo> staffByIds = permissionCenterClient.getStaffByIds(staffIds);
                        if (GeneralTool.isNotEmpty(staffByIds)) {
                            clientAgents.stream().forEach(data -> {
                                for (StaffVo staffById : staffByIds) {
                                    if (data.getFkStudentId().equals(clientData.getId())) {
                                        if (data.getFkStaffId().equals(staffById.getId())) {
                                            sb.append(data.getName()).append("，").append("BD：").append(staffById.getName()).append("；<br/>");
                                        }
                                    }
                                }
                            });
                        }
                    }
                    map.put(String.valueOf(clientData.getId()), sb.toString());
                }
            }
            if (!map.isEmpty()) {
                // 同名同生日不能提交
                map.put("status", "false");
                return map;
            }
        }

        List<ClientVo> hint = null;
        String msg = "";
        if (GeneralTool.isNotBlank(mobile)) {
            hint = BeanCopyUtils.copyListProperties(clientMapper.selectList(Wrappers.<Client>lambdaQuery()
                    .eq(Client::getMobile, mobile)
                    .eq(Client::getFkCompanyId, companyId)
                    .ne(GeneralTool.isNotEmpty(id), Client::getId, id)), ClientVo::new);
            msg = "mobile";
        }
        if (GeneralTool.isEmpty(hint) && GeneralTool.isNotBlank(email)) {
            hint = BeanCopyUtils.copyListProperties(clientMapper.selectList(Wrappers.<Client>lambdaQuery()
                    .eq(Client::getEmail, email)
                    .eq(Client::getFkCompanyId, companyId)
                    .ne(GeneralTool.isNotEmpty(id), Client::getId, id)), ClientVo::new);
            msg = "email";
        }

        switch (msg) {
            case "email":
                if (GeneralTool.isNotEmpty(email) && GeneralTool.isNotEmpty(hint)) {
                    for (ClientVo dto : hint) {
                        if (!dto.getId().equals(id)) {
                            temp++;
                            StringBuilder sb = new StringBuilder();
                            sb.append("【").append(temp).append("】")
                                    .append(LocaleMessageUtils.getMessage("m_client")).append("：").append(dto.getName()).append("，")
                                    .append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(dto.getNum()).append("，")
                                    .append(LocaleMessageUtils.getMessage("EMAIL")).append("：").append(email).append("，")
                                    .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！");
                            map.put(String.valueOf(dto.getId()), sb.toString());
                        }
                    }
                }
                break;
            case "mobile":
                if (GeneralTool.isNotEmpty(mobile) && GeneralTool.isNotEmpty(hint)) {
                    for (ClientVo dto : hint) {
                        if (!dto.getId().equals(id)) {
                            temp++;
                            StringBuilder sb = new StringBuilder();
                            sb.append("【").append(temp).append("】")
                                    .append(LocaleMessageUtils.getMessage("m_client")).append("：").append(dto.getName()).append("，")
                                    .append(LocaleMessageUtils.getMessage("NUMBERING")).append("：").append(dto.getNum()).append("，")
                                    .append(LocaleMessageUtils.getMessage("MOBILE")).append("：").append(mobile).append("，")
                                    .append(LocaleMessageUtils.getMessage("STUDENT_EXIST")).append("！");
                            map.put(String.valueOf(dto.getId()), sb.toString());
                        }
                    }
                }
        }
        if (GeneralTool.isEmpty(map)) {
            return null;
        }
        // 其他能提交
        map.put("status", "true");
        return map;
    }

    @Override
    public List<MediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_CLIENT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }


    @Override
    public List<MediaAndAttachedVo> addMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVo) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_CLIENT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }


    private Map<String, String> getCompanyMap() {
        //初始为5的map
        Map<String, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
            }
        }
        return companyMap;
    }

    private void setName(Map<String, String> companyMap, ClientVo clientVo, Map<Long, String> countryNamesByIds,
                         Map<Long, String> stateNamesByIds, Map<Long, String> cityNamesByIds, Map<Long, String> agentNameByClientIds,
                         Map<Long, String> bdCodeByClientIds) {

        if (GeneralTool.isNotEmpty(clientVo.getFkInstitutionIdEducation())) {
            clientVo.setEducationInstitutionName(institutionCenterClient.getInstitutionName(clientVo.getFkInstitutionIdEducation()).getData());
        }
        if (GeneralTool.isNotEmpty(clientVo.getFkInstitutionIdEducation2())) {
            clientVo.setEducationInstitutionName2(institutionCenterClient.getInstitutionName(clientVo.getFkInstitutionIdEducation2()).getData());
        }

        Map<Long, String> departmentNameMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(clientVo.getGmtCreateUser())) {
            StaffVo staffVo = permissionCenterClient.getStaffByCreateUser(clientVo.getGmtCreateUser()).getData();
            if (GeneralTool.isNotEmpty(staffVo.getFkDepartmentId())) {
                departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(Sets.newHashSet(staffVo.getFkDepartmentId())).getData();
                clientVo.setDepartmentName(departmentNameMap.get(staffVo.getFkDepartmentId()));
            }
        }

        //设置公司名
        clientVo.setFkCompanyName(companyMap.get(String.valueOf(clientVo.getFkCompanyId())));

        String cityName = cityNamesByIds.get(clientVo.getFkAreaCityId());
        String countryName = countryNamesByIds.get(clientVo.getFkAreaCountryId());
        String stateName = stateNamesByIds.get(clientVo.getFkAreaStateId());

        String educationCityName = cityNamesByIds.get(clientVo.getFkAreaCityIdEducation());

        String educationCountryName = GeneralTool.isNotEmpty(countryNamesByIds.get(clientVo.getFkAreaCountryIdEducation())) ? countryNamesByIds.get(clientVo.getFkAreaCountryIdEducation()) : clientVo.getFkAreaCountryName();
        String educationStateName = stateNamesByIds.get(clientVo.getFkAreaStateIdEducation());

        String educationCityName2 = cityNamesByIds.get(clientVo.getFkAreaCityIdEducation2());
        String educationCountryName2 = countryNamesByIds.get(clientVo.getFkAreaCountryIdEducation2());
        String educationStateName2 = stateNamesByIds.get(clientVo.getFkAreaStateIdEducation2());

        clientVo.setCityName(cityName);
        clientVo.setCountryName(countryName);
        clientVo.setStateName(stateName);

        clientVo.setCityNameEducation(educationCityName);
        clientVo.setCountryNameEducation(educationCountryName);
        clientVo.setStateNameEducation(educationStateName);


        clientVo.setCityNameEducation2(educationCityName2);
        clientVo.setCountryNameEducation2(educationCountryName2);
        clientVo.setStateNameEducation2(educationStateName2);

        clientVo.setFkAreaCityBirthName(cityNamesByIds.get(clientVo.getFkAreaCityIdBirth()));
        clientVo.setFkAreaCountryBirthName(countryNamesByIds.get(clientVo.getFkAreaCountryIdBirth()));
        clientVo.setFkAreaStateBirthName(stateNamesByIds.get(clientVo.getFkAreaStateIdBirth()));

        //国籍
        clientVo.setCountryNameNationality(countryNamesByIds.get(clientVo.getFkAreaCountryIdNationality()));
        //绿卡
        clientVo.setCountryNameGreenCard(countryNamesByIds.get(clientVo.getFkAreaCountryIdGreenCard()));


        //获取代理名称
        String agentName = agentNameByClientIds.get(clientVo.getId());
        List<String> agentNameList = GeneralTool.isEmpty(agentName) ? new ArrayList<>() : Arrays.asList(agentName);
        clientVo.setFkAgentName(agentNameList);

        //获取bd编号
        String bdCode = bdCodeByClientIds.get(clientVo.getId());
        List<String> bdCodeList = GeneralTool.isEmpty(bdCode) ? new ArrayList<>() : Arrays.asList(bdCode);
        clientVo.setFkStaffName(bdCodeList);

        if (GeneralTool.isNotEmpty(clientVo.getBirthday())) {
            clientVo.setAge(getAge(clientVo.getBirthday()));
        }
//        if (GeneralTool.isNotEmpty(clientVo.getConditionType())) {
//            String[] split = clientVo.getConditionType().split(",");
//            StringJoiner stringJoiner = new StringJoiner(" ");
//            for (String type : split) {
//                String valueByKey = ProjectExtraEnum.getValueByKey(Integer.valueOf(type), ProjectExtraEnum.STUDENT_BUSINESS_STATUS);
//                stringJoiner.add(valueByKey);
//            }
//            clientVo.setConditionTypeName(stringJoiner.toString());
//        }
        //高中成绩类型名称本科成绩类型名称
        if (GeneralTool.isNotEmpty(clientVo.getHighSchoolTestType())) {
            clientVo.setHighSchoolTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(clientVo.getHighSchoolTestType()), ProjectExtraEnum.HIGH_SCHOOL_GRADES));
        }
        if (GeneralTool.isNotEmpty(clientVo.getStandardTestType())) {
            clientVo.setStandardTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(clientVo.getStandardTestType()), ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT));
        }
        if (GeneralTool.isNotEmpty(clientVo.getEnglishTestType())) {
            clientVo.setEnglishTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(clientVo.getEnglishTestType()), ProjectExtraEnum.ENGLISH_TEST_TYPE));
        }
        if (GeneralTool.isNotEmpty(clientVo.getMasterTestType())) {
            clientVo.setMasterTestTypeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(clientVo.getMasterTestType()),
                    ProjectExtraEnum.UNDERGRADUATE_ACHIEVEMENT));
        }

    }


    /**
     * @Description: 激活禁用（true：激活 false：禁用）
     * @Author: Sam
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActive(ClientUpdateDto clientUpdateVo) {
        if (GeneralTool.isEmpty(clientUpdateVo.getIsActive()) || GeneralTool.isEmpty(clientUpdateVo.getId()))
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        Client client = clientMapper.selectById(clientUpdateVo.getId());
        if (GeneralTool.isEmpty(client))
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        // 改变状态
        utilService.setUpdateInfo(client);
        client.setIsActive(clientUpdateVo.getIsActive());
        clientMapper.updateById(client);
        // 新增作废备注
        CompanyConfigAnalysisVo configAnalysisDto = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.REMINDER_EMAIL_CLIENT.key).getData().get(SecureUtil.getFkCompanyId());
        // 如果激活不做其他操作
        if (clientUpdateVo.getIsActive()) {
            return;
        }
        CommentDto commentVo = new CommentDto();
        commentVo.setFkTableId(clientUpdateVo.getId());
        commentVo.setComment("作废原因:" + clientUpdateVo.getRemark());
        editComment(commentVo);
        //如果是废弃状态，发邮件给所配置的员工
        List<Long> staffIds = new ArrayList<>(JSON.parseArray(configAnalysisDto.getValue1(), Long.class));
        if (GeneralTool.isEmpty(staffIds)) {
            log.info("未配置废弃学生资源通知员工账号");
            return;
        }
        Map<String, String> map = new HashMap();
        map.put("name", client.getName());
        map.put("remark", clientUpdateVo.getRemark());
        map.put("gmtModifiedUser", client.getGmtModifiedUser());
        map.put("gmtModified", DateUtil.format(client.getGmtModified(), "yyyy-MM-dd").toString());
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(client.getFkCompanyId());
        map.put("versionValue", versionValue2);
        String title = null;
        if(GeneralTool.isEmpty(versionValue2)&&versionValue2.equals("en")){
            title = "[Invalid Student Resource Setting Notification]" + client.getName() + "Set As Invalid";
        }else {
           title = "【学生资源设置无效通知】" + client.getName() + "被设置无效";
        }
        if(GeneralTool.isNotEmpty(staffIds)) {
            List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
            EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
            emailSenderQueue.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            emailSenderQueue.setFkTableId(client.getId());
            emailSenderQueue.setFkTableName(TableEnum.SALE_CLIENT.key);
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.CLIENT_STUDENT_INVALID.getEmailTemplateKey());
            emailSenderQueue.setOperationTime(now());
            emailSenderQueue.setEmailTitle(title);
            emailSenderQueue.setEmailParameter(JSON.toJSONString(map));
            emailSenderQueue.setEmailTo(staffIds.toString());
            emailSenderQueueList.add(emailSenderQueue);
            Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        if (booleanResult.isSuccess() && booleanResult.getData())
                log.info("发送学生资源无效通知给员工：{}", staffIds.toString());
            else
                log.info("发送学生资源无效通知给员工失败：{}", staffIds.toString());
        }
        //TODO CLIENT_APPROVAL_NOTICE
//        map.put("taskTitle", title);
//        for (Long staffId : staffIds) {
//            String staffName = permissionCenterClient.getStaffName(staffId).getData();
//            map.put("staffName", staffName);
//            Result<Boolean> result = reminderCenterClient.sendEmailToStaff(title, "CLIENT_STUDENT_INVALID", staffId, JSON.toJSONString(map));
//            if (result.isSuccess() && result.getData())
//                log.info("发送学生资源无效通知给员工：{}", staffId);
//            else
//                log.info("发送学生资源无效通知给员工失败：{}", staffId);
//        }

    }

    @Override
    public List<BusinessStudentVo> getBusinessStudent(Long fkClientId) {
        List<BusinessStudentVo> businessStudentVoList = new ArrayList<>();
        Long staffId = SecureUtil.getStaffId();
        boolean staffInfoFlag = GeneralTool.isNotEmpty(SecureUtil.getStaffInfoByStaffId(staffId));
        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);


        StudentListQueryDto studentListQueryBo = new StudentListQueryDto();
        List<Long> list = new ArrayList<>();
        list.add(3L);
        list.add(32L);
        list.add(31L);
        list.add(30L);
        studentListQueryBo.setFkCompanyIds(list);
        studentListQueryBo.setGeaConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_CONFIRMATION_STATISTICS_STEP));
        studentListQueryBo.setGeaSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.GEA_SUCCESS_STATISTICS_STEP));
        studentListQueryBo.setIaeConfirmationStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_CONFIRMATION_STATISTICS_STEP));
        studentListQueryBo.setIaeSuccessStatisticsStepList(ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.IAE_SUCCESS_STATISTICS_STEP));
        studentListQueryBo.setJumpState(1);
        studentListQueryBo.setFkCompanyId(3L);
        //根据资源id招到对应的学生
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Student::getFkClientId,fkClientId);
        List<Student> collect = studentMapper.selectList(wrapper);
        if(GeneralTool.isEmpty(collect)){
            return businessStudentVoList;
        }

        List<StudentVo> studentVoList = new ArrayList<>();
        for (Student student:collect){
            // 使用 BeanCopyUtils 将 Student 转换为 StudentVo
            StudentVo studentVo = BeanCopyUtils.objClone(student, StudentVo::new);
            // 将转换后的 StudentVo 添加到集合中
            studentVoList.add(studentVo);
        }

        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FINANCE_OPENING_TIME_COUNT.key).getData();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date beginOpenTime;
        Date endOpenTime;
        try {
            beginOpenTime = sf.parse(configVo.getValue1());
            endOpenTime = sf.parse(configVo.getValue2());
        } catch (ParseException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }

        List<Long> studentIds = collect.stream().map(Student::getId).collect(Collectors.toList());
        List<StudentItemStatusVo> studentItemStatus = Lists.newArrayList();
        List<Long> fkAreaCountryIds = SecureUtil.getCountryIds();
        Boolean isBd = studentOfferService.getIsBd(SecureUtil.getStaffId());
        if (staffInfoFlag){
            //根据学生ids查询学生的最高最低状态
            studentItemStatus = studentMapper.getStudentItemStatus(studentIds, studentListQueryBo,
                    staffFollowerIds, fkAreaCountryIds, ProjectKeyEnum.STEP_FAILURE.key,
                    SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentOfferItemFinancialHiding(),
                    SecureUtil.getStaffInfoByStaffId(staffId).getIsStudentAdmin(),
                    staffId, beginOpenTime, endOpenTime,isBd,
                    SecureUtil.getPermissionGroupInstitutionIds(),
                    SecureUtil.getStaffBoundBdIds());

        }
        for (StudentVo studentVo :studentVoList ) {
            for (StudentItemStatusVo studentStatus : studentItemStatus) {
                if (studentVo.getId().equals(studentStatus.getId())) {
                    studentVo.setMaxStepOrder(studentStatus.getMaxStepOrder());
                    studentVo.setMinStepOrder(studentStatus.getMinStepOrder());
                }
            }
        }

        //获取步骤id和步骤名
        List<StudentOfferItemStep> studentOfferItemSteps = studentOfferItemStepMapper.selectList(Wrappers.<StudentOfferItemStep>lambdaQuery());
        HashMap<Integer, String> studentOfferItemStepMap = new HashMap<>();
        for (StudentOfferItemStep studentOfferItemStep : studentOfferItemSteps) {
            studentOfferItemStepMap.put(studentOfferItemStep.getStepOrder(), studentOfferItemStep.getStepName());
        }

        //申请国家/地区
        Set<Long> countryIds = collect.stream().map(Student::getFkAreaCountryId).collect(Collectors.toSet());

        List<StudentOffer> studentOffers = new ArrayList<>();
        List<Long> studentIds_ = collect.stream().map(Student::getId).distinct().collect(Collectors.toList());

        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(studentIds_)) {
            studentOffers = studentOfferService.getStudentOffersByStudentIds(studentIds_);
            if (GeneralTool.isNotEmpty(studentOffers)) {
                countryIds.addAll(studentOffers.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet()));
            }
            if (GeneralTool.isNotEmpty(countryIds)) {
                if (GeneralTool.isNotEmpty(countryIds)) {
                    countryNamesByIds = institutionCenterClient.getCountryNamesByIds(countryIds).getData();
                }
            }
        }

        //获取代理名称
        //根据员工ids获取姓名
        Map<Long, String> staffNamesByIds = new HashMap<>();
        Set<Long> allStaffIds = permissionCenterClient.getAllStaffIds();
        staffNamesByIds = permissionCenterClient.getStaffNamesByIds(allStaffIds);

        //获取学生
        List<Long> studentIds1 = collect.stream().map(Student::getId).collect(Collectors.toList());
        //根据学生ids查询代理和BD
        List<Map<String, Object>> studentAgentBdList = studentOfferItemMapper.getAgentBdByStudentId(studentIds1);
        studentAgentBdList.removeIf(Objects::isNull);

        //获取代理id
        Set<Long> fkAgentIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(studentOffers)) {
            fkAgentIds = studentOffers.stream().map(StudentOffer::getFkAgentId).collect(Collectors.toSet());
        }
        //根据代理ids获取名称
        Map<Long, Agent> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAgentIds)) {
            agentNamesByIds = agentService.getAgentsByIds(fkAgentIds);
        }


        // 在循环外预先处理studentOffers
        Map<Long, List<StudentOffer>> studentOffersMap = studentOffers.stream()
                .collect(Collectors.groupingBy(StudentOffer::getFkStudentId));
        for (StudentVo studentVo :studentVoList ){
            List<StudentOffer> studentOffersFor = studentOffersMap.getOrDefault(studentVo.getId(), Collections.emptyList());
            setName(studentVo,studentOffersFor,countryNamesByIds,staffNamesByIds,studentAgentBdList,studentOfferItemStepMap,agentNamesByIds);
            BusinessStudentVo businessStudentVo = BeanCopyUtils.objClone(studentVo, BusinessStudentVo::new);
            businessStudentVoList.add(businessStudentVo);
        }

        return businessStudentVoList;
    }

    @Override
    public List<BaseSelectEntity> getSubordinatesOfTheCurrentUserSelect(List<Long> companyIds) {
        List<BaseSelectEntity> resultList = new ArrayList<>();
        // 1. 获取下属员工ID列表
        Long staffId = SecureUtil.getStaffId();
        Result<List<Long>> followerResult = permissionCenterClient.getStaffFollowerIds(staffId);
        if (!followerResult.isSuccess() || GeneralTool.isEmpty(followerResult.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = Optional.ofNullable(followerResult.getData()).orElse(Collections.emptyList());
                staffFollowerIds.add(staffId);

        staffFollowerIds.add(staffId);
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            return resultList;
        }
        // 2. 批量获取员工姓名
        Set<Long> ids = new HashSet<>(staffFollowerIds);
        StaffByIdsAndCompanyIdsDto staffByIdsAndCompanyIdsDto = new StaffByIdsAndCompanyIdsDto();
        staffByIdsAndCompanyIdsDto.setStaffIds(ids);
        staffByIdsAndCompanyIdsDto.setCompanyIds(companyIds);
        Map<Long, String> nameResult = permissionCenterClient.getStaffListByIdsAndCompanyIds(staffByIdsAndCompanyIdsDto);

        // 3. 构造 BaseSelectEntity 列表
        if (GeneralTool.isEmpty(nameResult)){
            return resultList;
        }

        nameResult.forEach((id, name) -> {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setId(id);
            baseSelectEntity.setName(name);
            resultList.add(baseSelectEntity);
        });

        return resultList;
    }

    public void setName(StudentVo student,
                        List<StudentOffer> studentOffers,
                        Map<Long, String> countryNamesByIds,
                        Map<Long, String> staffNamesByIds,
                        List<Map<String, Object>> studentAgentBdList,
                        Map<Integer, String> studentOfferItemStepMap,
                        Map<Long, Agent> agentNamesByIds){

        //设置最终状态名称
        student.setMaxStepOrderName(studentOfferItemStepMap.get(student.getMaxStepOrder()));

        //设置申请国家和地区
        Set<Long> offerCountryIds = new HashSet<>();
        offerCountryIds.addAll(studentOffers.stream().map(StudentOffer::getFkAreaCountryId).collect(Collectors.toSet()));
//        //根据国家ids获取国家名称
        Map<Long, String> offerCountryNamesByIds = new HashMap<>();
        countryNamesByIds.forEach((key, value) -> {
            if (offerCountryIds.contains(key)) {
                offerCountryNamesByIds.put(key, value);
            }
        });
        if (GeneralTool.isNotEmpty(offerCountryNamesByIds)) {
            student.setAreaCountryNames(offerCountryNamesByIds.values().stream().collect(Collectors.joining(",")));
        }

        //设置代理编号
        //当前绑定代理
        Set<String> currentStaffNameList = new LinkedHashSet();
        //当前绑定代理编号
        Set<String> currentAgentNumList = new LinkedHashSet<>();
        //当前绑定BD
        Set<String> currentBdNameList = new LinkedHashSet();

        Long studentId = student.getId();
        for (Map<String, Object> studentAgentBd : studentAgentBdList) {
            //获取当前学生不为空的代理和不为空的员工
            if (studentAgentBd.get("studentId").equals(studentId)
                    && GeneralTool.isNotEmpty(studentAgentBd.get("fkAgentId"))
                    && GeneralTool.isNotEmpty(studentAgentBd.get("fkStaffId"))) {
                StringBuilder sb = new StringBuilder();
                //获取代理名称
                String agentName = "";
                String nameNote = "";
                String agentNum = null;
                Long fkAgentId = Long.valueOf(studentAgentBd.get("fkAgentId").toString());
                if (agentNamesByIds.containsKey(fkAgentId)) {
                    Agent agent = agentNamesByIds.get(fkAgentId);
                    agentName = agent.getName();
                    nameNote = agent.getNameNote();
                    agentNum = agent.getNum();
                    if (StringUtils.isNotBlank(nameNote)) {
                        agentName += "（" + nameNote + "）";
                    }
                }
                //获取员工姓名
                String staffName = staffNamesByIds.get(Long.valueOf(studentAgentBd.get("fkStaffId").toString()));
                if (GeneralTool.isNotEmpty(agentName) && GeneralTool.isNotEmpty(staffName)) {
                    //格式：代理名称A（员工BD名称A）
                    sb.append(agentName).append("（").append(staffName).append("）");
                    if (StringUtils.isNotBlank(nameNote)) {
                        sb.append("（").append(nameNote).append("）");
                    }
                    currentStaffNameList.add(agentName);
                    currentBdNameList.add(staffName);
                  /*  currentBdNameAndStaffNameList.add(sb.toString());*/
                }
                //代理编号
                if(GeneralTool.isNotEmpty(agentNum)){
                    currentAgentNumList.add(agentNum);
                }

            }
        }
        student.setCurrentStaffNameList(currentStaffNameList);
        student.setCurrentAgentNumList(currentAgentNumList);
        student.setCurrentBdNameList(currentBdNameList);


    }
    @Override
    public Client getClientById(Long id) {
        return clientMapper.selectById(id);
    }



}
