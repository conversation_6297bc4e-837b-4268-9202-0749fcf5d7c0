package com.get.remindercenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.remindercenter.dto.*;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.vo.*;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IReminderCenterClientFallBack implements IReminderCenterClient {
//    @Override
//    public Result<List<ContactPersonTypeVo>> getAllType() {
//        return Result.fail("数据查询失败");
//    }

    @Override
    public Result<Boolean> addTask2NewsEmailQueen(AliyunSendMailDto aliyunSendMailVo) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<String>> getDefaultSendEmail() {
        return null;
    }

    @Override
    public Result<Boolean> batchAdd(List<RemindTaskDto> remindTaskDtos) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> batchUpdate(List<RemindTaskDto> remindTaskDtos, String fkTableName, Long fkTableId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> batchUpdateNew(List<RemindTaskDto> remindTaskDtos) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> batchUpdateTaskNew(List<RemindTaskDto> remindTaskDtos) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> batchDeleteTaskNew(List<RemindTaskDto> remindTaskDtos) {
        return null;
    }

    @Override
    public Result<Boolean> batchUpdateImport(List<RemindTaskDto> remindTaskDtos) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> batchAddTask(List<RemindTaskDto> remindTaskDtos) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> batchDeleteByTableId(String fkTableName, Long fkTableId, List<String> fkRemindEventTypeKeys) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> deleteEmailQueueByTableId(List<RemindTaskDto> remindTaskDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> batchUpdateByTableIds(List<RemindTaskDto> remindTaskDtos, String fkTableName, Set<Long> fkTableIds) {
        return Result.fail("操作失败");
    }

    @Override
    public void performTasks() {
        Result.fail("操作失败");
    }

    @Override
    public void sendEmailScheduleTask() {
        Result.fail("操作失败");
    }
//    @Override
//    public Result<String> getCurrencyNameByNum(String fkCurrencyTypeNum) {
//        return Result.fail("获取数据失败");
//    }
//
//    @Override
//    public Result<Map<String, String>> getCurrencyNamesByNums(Set<String> fkCurrencyTypeNums) {
//        return Result.fail("获取数据失败");
//    }


    @Override
    public Result<Boolean> sendSms(SmsDto smsDto) {
        return null;
    }

    @Override
    public Result add(RemindTaskUpdateDto remindTaskUpdateDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> sendMail(String title, String template, String toEmail, String ccEmail) {
        return Result.data(false);
    }

//    @Override
//    public void sendEmail(String title, String typeKey, Long fkStaffId, String taskRemark) {
//
//    }

    @Override
    public void batchSendEmail(List<Map<String, String>> list, String typeKey) {

    }

    @Override
    public void batchSendEnEmail(List<Map<String, String>> list, String typeKey,String version) {

    }


    @Override
    public void batchSendEmailCustom(MailDto mailDto) {

    }

    @Override
    public Result<Boolean> sendEmailToStaff(String title, String typeKey, Long fkStaffId, String taskRemark) {
        return Result.data(false);
    }

    @Override
    public Result<Boolean> customSendMail(String defaultEncoding, String host, int port, String protocol, String userName, String password, String title, String toEmail, String ccEmail, String template, boolean flag) {
        return Result.data(false);
    }

    @Override
    public Result<Boolean> customSendMailByBody(MailDto mailDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<RemindTemplate> getRemindTemplateByTypeKey(String typeKey) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<EmailTemplate> getEmailTemplateByTypeKey(String typeKey) {
        return Result.fail("操作失败");
    }


    @Override
    public Result<Long> aliyunAddTag(String TagName) {
        return Result.data(null);
    }

    @Override
    public Result<List<BatchEmailPromotionQueueVo>> getSendNewsEamilQueues() {
        return Result.data(Collections.EMPTY_LIST);
    }

    @Override
    public Result<Boolean> sendNewsEmail(AliyunSendMailDto aliyunSendMailVo) {
        return Result.fail("发送失败");
    }

    @Override
    public Result<Boolean> deleteMsgFromQueue(Long id) {
        return Result.fail("删除新闻邮件队列失败");
    }

    @Override
    public Result<Boolean> aliyunSendMailNew(AliyunSendMailDto aliyunSendMailVo) {
        return null;
    }

    @Override
    public Result<Boolean> sendSystemMail(EmailSystemMQMessageDto emailSystemMQMessageDto) {
        return null;
    }

    @Override
    public Result<Boolean> sendCustomMail(EmailCustomMQMessageDto emailCustomMQMessageDto) {
        return null;
    }

    @Override
    public Result<Boolean> sendMqEmail(EmailSenderQueue emailSenderQueue) {
        return null;
    }

    @Override
    public Result<Boolean> sendMqEmailTask(EmailSenderQueue emailSenderQueue) {
        return null;
    }

    @Override
    public Result<Boolean> batchAddEmailQueue(List<EmailSenderQueue> queueList) {
        return Result.data(false);
    }

    @Override
    public Result<Boolean> updateEmailQueue(EmailSenderQueue emailSenderQueue) {
        return Result.data(false);
    }

}
